import request from '@/utils/request'
import { pref } from '@/utils/common'
import store from '@/store'

// 查询微信公众号配置
export const wxConfig = () => request.get('/sso-center-api/account/auth/wxConfig')

// 微信扫码登录
export const parentOpenLogin = params => request.get(`/sso-center-api/account/auth/parentOpenLogin?code=${params.code}&state=${params.state}`)

// 登录
export const login = params => request.post('/sso-center-api/account/login', params)

// 省平台登录
export const provPltFmLogin = token => request.get('/sso-center-api/oauth2/login?token=' + token)

// 冀时办登录
export const jsbLogin = token => request.get('/sso-center-api/oauth2/loginByJsb?token=' + token)

// 退出
export const logout = params => request.post('/sso-center-api/account/logout', params)

// 查询系统名称
export const getSystemName = () => request.post(`${pref}${store.state.deptCode}/biz/stuPhase/getSystemName`)

// 政策公告 - 列表
export const getPolicyList = params => request.post(`${pref}${store.state.deptCode}/biz/messagePolicy/list`, params)
// 政策公告 - 是否开启按钮
export const permissionStatus = params => request.post(`${pref}${store.state.deptCode}/biz/messagePolicy/status`, params)

// 政策公告 - 详情
export const getPolicyDetail = params => request.post(`${pref}${store.state.deptCode}/biz/messagePolicy/detail`, params)

// // 学校列表
export const deptPageLists = params => request.post('/user-api/center/dept/pageList', params)
// 学校列表
export const xiaoquList = params => request.post('/user-api/center/schoolRang/xiaoquList', params)
// 学校列表
export const deptPageList = params => request.post('/user-api/center/dept/schoolPageList', params)

// 学校详情
export const schoolDetail = params => request.post('/user-api/center/dept/detail', params)

// 学校 - 招生范围
export const schoolAdRange = params => request.post('/user-api/center/schoolRang/list', params)

// 查询报名入口
// 第一级：报名 - 选择乡镇、城区
// 第二级：报名 - 选择学段
export const getSetupSaveIds = params => request.post(`${pref}${store.state.deptCode}/biz/enrollment/getSetupSaveIds`, params)
// 第三级：报名 - 选择类别(户口报名、房产报名等)
export const getSetupSaveDetail = params => request.post(`${pref}${store.state.deptCode}/biz/enrollment/getSetupSaveDetail`, params)

/**** 录取 待验证 ****/
// 录取查询 - 获得页面显示类型
export const qryFirst = (params) => request.post(`${pref}${store.state.deptCode}/biz/enrollQuery/onePage`, params)

// 录取查询 - 根据身份证号查询
export const searchResById = (params) => request.post(`${pref}${store.state.deptCode}/biz/enrollQuery/search`, params)

// 录取查询 - 录取通知书
export const notifyDetail = (params) => request.post(`${pref}${store.state.deptCode}/biz/enrollQuery/requisition`, params)

// 录取查询 - 绑定指定身份证号到当前微信
export const bindIdCard2CurWx = params => request.post(`${ pref }${store.state.deptCode}/biz/enrollQuery/stuBindingWeChat`, params)

// 是否报名时间内
export const isInAdTimeRange1 = (params) => request.post(`${pref}${store.state.deptCode}/biz/registrationTime/homePageVerify`, params)

// 是否报名时间内
export const isInAdTimeRange2 = (params) => request.post(`${pref}${store.state.deptCode}/biz/registrationTime/getVerifyBySetUpId`, params)

// 根据入口查询对应报名表单
export const qryAdFormByEntry = params => request.post(`${pref}${store.state.deptCode}/biz/enrollment/getEnrollFieldConfig`, params)

// 根据学生id查询已报名表单
export const qryEditAdForm = params => request.post(`${pref}${store.state.deptCode}/biz/enrollment/modifyGetEnrollFieldConfig`, params)

// 报名提交
export const submitAd = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/submitEnrollInfo`, params)

// 驳回或修改后的报名提交
export const submitAdSecond = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/rejectOrModifyEnrollInfo`, params)

// 报名详情
export const adFormDetail = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/getStuEnrollInfo`, params)

// 录取查询 - 获取跳转类型
export const getNextPath = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/getNextPath`, params)

// 报名详情页 - 剩余修改次数
export const adRemainEditCount = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/getResidueModifyCount`, params)

// 报名详情页 - 删除报名数据
export const delAd = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/parentClearEnrollInfo`, params)

// 报名修改页 - 获取所有报名类型
export const getAllAdTp = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/getEnrollEntrance`, params)

export const getAllAdTp1=params=>request.post(`${pref}${ store.state.deptCode }/biz/enrollment/getSchoolEnrollEntrance`,params)
// 删除上传的图片
export const delImgUpload = (data) => request.post(`${pref}${ store.state.deptCode }/biz/file/deleteImg`, data)
/**** 录取 ****/











// 获取图片验证码
// export const getPicVerifyCode = () => request({url:'/site/account/getPicVerifyCode', method:'get'})

// 修改密码
// export const changePassword = params => request.post('/site/account/changePassword', params)

// 忘记、重置密码
// export const resetPassword = params => request.post('/site/account/resetPassword', params)

// 获取手机验证码
// export const getSmsVerifyCode = params => request.post('/site/account/getSmsVerifyCode', params)

// 政策公告查询列表 - 首页
// export const articlePolicyListHome = params => request.post('/site/index/index', params)



// 查询中考报名须知
export const enrolmentNotes = (grade = 9, params) => request.post(`/site/enrolmentNotes/get/${grade}`, params)

// 获取当前账户信息
export const getSelfInfo = params => request.post('/site/account/getSelfInfo', params)

// 获取民族列表、考生类别、优惠类别
export const getUpdatePage = params => request.post('/site/account/updatePage', params)

// 修改报名信息
export const update = params => request.post('/site/account/update', params, { timeout: 10000 })

// 获取报名时间
export const getSigUpTime = params => request.post('/site/account/getSigUpTime', params)

// 根据类型获取时间设置
export const getSigUpTimeByType = (type, grade = 9) => request.post(`/site/account/getSigUpTimeByType/${type}/${grade}`)

// 获取志愿填报须知
export const getVolunteerNotes = () => request.post('/site/volunteer/notes/getNotes')

// 查询中考成绩
export const scoreSearch = params => request.post('/site/score/search', params)

// 录取结果查询
export const enrollSearch = params => request.post('/site/enroll/record/search', params)

// 获取省市区数据
export const getDistrictList = params => request.post('/gaode/getDistrictList', params)

// 获取所有招生计划
export const getAllEnrollPlan = () => request.post('/site/volunteer/getAllEnrollPlan')
export const getAllZhngJiEnrollPlan = (zhengJiType) => request.post('/site/volunteer/zhengji/getAllEnrollPlan', { key: zhengJiType })

// 考生新增/修改志愿填报数据
export const saveVolunteer = (params) => request.post('/site/volunteer/saveVolunteer', params)
export const saveZhengJiVolunteer = (params) => request.post('/site/volunteer/saveZhengJiVolunteer', params)

// 获取考生志愿信息
export const getSelfVolunteerData = () => request.post('/site/volunteer/getSelfVolunteerForm')
export const getSelfZhengJiVolunteerData = () => request.post('/site/volunteer/getSelfZhengJiVolunteerForm')
