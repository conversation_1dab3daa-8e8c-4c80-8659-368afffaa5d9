<template>
	<!-- 报名详情查看：图片 -->
	<div class="normal-img-ex">
		<el-image
			style="width: 148px; height: 148px"
			:src="modelData" 
			:preview-src-list="previewList"
		>
			<div slot="error" class="ph">
				暂无图片
			</div>
		</el-image>
		<div class="key-desc">
		  {{ keyDesc }}
		</div>
	</div>
</template>

<script>
import { imgPrefix } from '@/utils/common'
export default {
	name: 'normal-img-ex',
	data() {
		return {
			modelData: '',
			keyDesc: '',
			previewList: []
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		}
	},
	created() {
		this.keyDesc = this.itemConfig.fieldName
		if (this.itemConfig.fieldValue) {
			this.modelData = `${ imgPrefix() }${ this.itemConfig.fieldValue }`
			this.previewList = [this.modelData]
		}
	}
}
</script>

<style>

</style>