<template>
  <div class="header">
    <div class="header-title">
      <div class="header-title-main">
        <span class="title">
          {{ systemName }}
        </span>
        <!-- <img :src="require('@/assets/img/header-pic.png')" alt="" style="height: 180px;"> -->
      </div>
    </div>
    <div class="header-nav">
      <div class="header-nav-main">
        <el-menu
          class="title-strong"
          :default-active="navselected"
          :active="navselected"
          @select="selectNav"
          background-color="transparent"
          text-color="#000"
          active-text-color="#0980BC"
        >
          <el-menu-item index="/home">
            <span slot="title" disabled="true">首页</span>
          </el-menu-item>
          <el-menu-item index="/policyAnnouncement">
            <span slot="title">政策公告</span>
          </el-menu-item>
          <el-menu-item index="/enrollRange">
            <span slot="title">招生范围</span>
          </el-menu-item>
          <el-menu-item index="/consultingService">
            <span slot="title">咨询电话</span>
          </el-menu-item>
          <el-menu-item  v-if="showAdmissionButton" index="/admissionInquiry">
            <span slot="title">录取查询</span>
          </el-menu-item>
          <!-- <el-menu-item index="/personalCenter">
            <span slot="title">个人中心</span>
          </el-menu-item> -->
        </el-menu>
        <el-button
          type="text"
          v-if="!user.username"
          class="header-nav-loginButton"
          @click="login"
          >登录</el-button
        >
        <div class="user-info" v-if="user.nickname">
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              {{ user.nickname }}
            </span>
            <el-dropdown-menu slot="dropdown" size="mini">
              <el-dropdown-item icon="el-icon-right" command="logout"
                >退出系统</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- <el-dropdown class="el-dropdown" @command="handleCommand">
        <div style="text-align: -webkit-center">
            <span class="el-dropdown-link" >
                <i class="el-icon-user-solid" style="font-size: 20px"></i>
            </span>
        </div>
        <el-dropdown-menu slot="dropdown" size="mini">
            <el-dropdown-item icon="el-icon-edit" command="a">修改密码</el-dropdown-item>
            <el-dropdown-item icon="el-icon-back" command="b">退出系统</el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown> -->

    <!-- <el-dialog title="修改密码"
        :visible.sync="dialogFormVisible"
        width="400px"
        center
        class="edit-password"
        :close-on-click-modal="false"
        >
        <el-form :model="editForm" :rules="rules" ref="editForm" label-width="100px" label-position="right" style="width: 300px">
            <el-form-item label="设置密码" prop="password">
                <el-input type="password" v-model="editForm.password" autocomplete="off" placeholder="8~16位数字与字母组合"></el-input>
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
                <el-input type="tel" v-model="editForm.phone" autocomplete="off" placeholder="输入手机号" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="验证码" prop="verifyCode" >
                <el-input v-model="editForm.verifyCode" autocomplete="off" placeholder="输入验证码" style="width: 120px"></el-input>
                <input
                            class="sendcode"
                            type="text"
                            id="code"
                            readOnly="true"
                            value="获取验证码"
                            @click="sendCode"
                        />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false"
                    >取消</el-button
                >
                <el-button
                    type="primary"
                    @click="submitForm('editForm')"
                    >提交</el-button
                >
            </div>
    </el-dialog> -->
  </div>
</template>

<script>
import store from "@/store";
import { mapState } from "vuex";
import { baseDomain } from "@/settings.js";
import { areaList } from "@/utils/dictionary.js"
import { getSystemName,
  permissionStatus} from "@/api"
export default {
  data() {
    return {
      navselected: "", //当前菜单标识
      systemName: "",
      permissionInfo: null, // 权限状态信息
      showAdmissionButton: false, // 是否显示录取查询按钮
    };
  },
  created() {
    // this.editForm.id = this.user.id
    if (this.currentNav) {
      this.navselected = this.currentNav;
    }
    this.getPermissionStatus(); // 获取权限状态
    this.getSystemName();
  },
  computed: {
    ...mapState(["currentNav", "user"]),
  },
  watch: {
    currentNav(newVal) {
      this.navselected = newVal;
    },
    "$store.state.deptCode"(newV, oldV) {
      if (newV != oldV) {
        this.getSystemName();
      }
    },
  },
  methods: {
    getPermissionStatus() {
      permissionStatus().then((res) => {
        this.permissionInfo = res;
        // 根据isAnNiu字段控制录取查询按钮显示：1显示，0不显示
        this.showAdmissionButton = res && res.isAnNiu === 1;
      }).catch((error) => {
        console.error('获取权限状态失败:', error);
        // 出错时默认不显示按钮
        this.showAdmissionButton = false;
      });
    },
    // 选择菜单
    selectNav(index) {
      // 存储当前导航路由
      store.commit("setCurrentNav", index);
      this.$router.push(index);
    },

    // 用户下拉菜单
    handleCommand(command) {
      switch (command) {
        case "logout":
          // 退出系统
          this.handleLogout();
          break;
        default:
          break;
      }
    },

    // 登录
    login() {
      this.$router.push("/loginWx");
    },

    // 退出系统
    handleLogout() {
      // 默认loginTp是0时，退到根目录
      let returnUrl = location.origin
      // 省平台，退到sso.html
      if (this.$store.state.loginTp == 'prov') {
      	returnUrl += '/sso.html'
      }
      this.$store.dispatch("logout").finally(() => {
      	location.href = returnUrl
      })
    },

    // 获取系统名称
    getSystemName() {
      // this.systemName = `${ areaList.find(v => v.id == this.$store.state.deptCode).val }教育入学一件事服务平台`
			getSystemName().then((res) => {
			  this.systemName = res;
			})
    },
  },
};
</script>

<style scoped lang="scss">
.header-title {
  width: 100%;
  height: 140px;
  line-height: 140px;
}

.header-title-main {
  width: 1240px;
  height: 100%;
  margin: 0 auto;
  position: relative;
}

.header-title-main .title {
  position: absolute;
  color: #fff;
  font-size: 32px;
  font-weight: bold;
  letter-spacing: 2px;
  text-shadow: 3px 3px rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.header-title-main img {
  position: absolute;
  top: 10px;
  right: 40px;
}

.header-nav {
  width: 1240px;
  margin: 0 auto;
  height: 57px;
  background-color: #fff;
  border-radius: 4px;
}

.header-nav-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
}

.header-nav-main .header-nav-loginButton {
  position: absolute;
  top: 9px;
  right: 0;
  color: #fff;
  font-size: 16px;
  transition: 0.3s all;
}

.header-nav-main .user-info {
  padding-right: 15px;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
}

.header-nav-main .header-nav-loginButton:hover {
  opacity: 0.7;
}

.el-dropdown-link {
  display: inline-block;
  color: #000;
  font-size: 16px;
  width: 600px;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-dialog__header,
.dialog-footer {
  line-height: 56px;
}

.sendcode {
  float: right;
  width: 68px;
  height: 35px;
  cursor: pointer;
  text-align: center;
  border: 1px solid #409eff;
  border-radius: 4px;
  background-color: #fff;
  outline: none;
  color: #409eff;
}

.el-menu {
  width: 50%;
  border-right: 0;
}

.el-menu-item {
  float: left;
  height: 56px;
  line-height: 56px;
  padding: 0 22px;
  font-size: 16px;
  background-color: transparent !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.el-menu-item:hover {
  background-color: transparent !important;
}

.el-menu-item span {
  position: relative;
  top: -3px;
  color: #000;
  transition: all 0.2s;
}

.el-menu-item.is-active span,
.el-menu-item:hover span {
  color: #0980bc;
  &:after {
    content: "";
    position: absolute;
    bottom: -7px;
    left: 50%;
    transform: translate(-50%, 0);
    width: 80%;
    height: 0;
    border-bottom: 2px solid;
  }
}
</style>
