<template>
  <div v-if="readonly==true || readonly=='true'">{{ itemText }}</div>
  <div v-else>
    <el-cascader :options="cityOptions" v-model="selectedItems"
                 :size="size"
                 placeholder="户口所在地"
                 :props="cityProps"
                 @change="change"
                 style="width: 100%"
    ></el-cascader>
  </div>
</template>

<script>
import {getDistrictList} from '@/api'

export default {
  name: "DistrictSelector",
  data() {
    return {
      selectedItems: [],
      cityOptions: [],
      cityProps: {
        label: 'name',
        value: 'name',
        children: 'districts'
      }
    }
  },
  props: {
    item: {
      type: String | Array,
      default: ''
    },
    readonly: {
      type: Boolean | String,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    size: {
      type: String,
      default: ''
    }
  },
  model: {
    prop: 'item',
    event: 'change'
  },
  created() {
    if (this.item) {
      if (this.item instanceof Array) {
        this.selectedItems = [...this.item]
      } else {
        this.selectedItems = this.item.split("/")
      }
    }
    this.getCities()
  },
  computed: {
    itemText() {
      if (this.selectedItems) {
        return this.selectedItems.join("/")
      } else {
        return ''
      }
    }
  },
  methods: {
    getCities() {
      if (sessionStorage.getItem("hscc-chengde-cities")) {
        this.cityOptions = JSON.parse(sessionStorage.getItem("hscc-chengde-cities"))
      } else {
        getDistrictList().then(res => {
          this.cityOptions = res.data;
          if (this.cityOptions && this.cityOptions.length) {
            sessionStorage.setItem("hscc-chengde-cities", JSON.stringify(res.data))
          }
        })
      }
    },
    change(items) {
      this.selectedItems = items
      if (this.item) {
        if (this.item instanceof Array) {
          this.$emit('change', items)
        } else {
          this.$emit('change', items.join("/"))
        }
      } else {
        this.$emit('change', items.join("/"))
      }
    }
  }
}
</script>

<style scoped>

</style>
