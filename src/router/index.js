import Vue from 'vue'
import VueRouter from 'vue-router'
import { Message } from 'element-ui'
import store from '@/store'
import { baseUrl } from '@/settings.js'

Vue.use(VueRouter)

const routes = [
  {
    path: '/loginWx',
    name: 'loginWx',
    component: () => import('@/views/loginWx'),
		beforeEnter: (to, from, next) => {
			let ticketIdxInUrl = location.search.indexOf('ticket=')
			// 如果地址栏里包含ticket，跳转至冀时办中转页
			if (ticketIdxInUrl != -1) {
				next('/jsbLogin')
			} else {
				next()
			}
		},
  },
	// 冀时办登录
	{
		path: '/jsbLogin',
		name: 'jsbLogin',
		component: () => import('@/views/loginWx/jsbLogin'),
	},
	{
	  path: '/auth',
	  name: 'auth',
	  component: () => import('@/views/loginWx/auth'),
	},
  {
    path: '/',
    name: 'layout',
    component: () => import('@/views/layout'),
    redirect: '/loginWx',
    children: [
      {
        path: '/home',
        component: () => import('@/views/home'),
        meta: { title: '首页' }
      },
			{
				path: '/areaSelect',
				component: () => import('@/views/area-select'),
				meta: { title: '区域选择' }
			},
      {
        path: '/policyAnnouncement',
        component: () => import('@/views/policyAnnouncement/index'),
        meta: { title: '政策公告' }
      },
      {
        path: '/policyAnnouncementDtl',
        component: () => import('@/views/policyAnnouncement/policyAnnouncementDtl'),
        meta: { title: '政策公告详情' }
      },
      {
        path: '/enrollRange',
        component: () => import('@/views/enrollRange/index'),
        meta: { title: '招生范围' }
      },
      {
        path: '/consultingService',
        component: () => import('@/views/consultingService'),
        meta: { title: '咨询电话' }
      },
      {
        path: '/admissionInquiry',
        component: () => import('@/views/admissionInquiry'),
        meta: { title: '录取查询' }
      },
      {
        path: '/enrollRangePrimary',
        component: () => import('@/views/enrollRange/enrollRangePrimary'),
        meta: { title: '小学招生范围' }
      },
      {
        path: '/enrollRangeJunior',
        component: () => import('@/views/enrollRange/enrollRangeJunior'),
        meta: { title: '初中招生范围' }
      },
      {
        path: '/enrollPeriods',
        component: () => import('@/views/enrollPeriods'),
        meta: { title: '报名入口 - 选择报名学段' },
				beforeEnter: (to, from, next) => {
					// 检查是否有入口id
					if (store.state.entry1.id) {
						next()
					} else {
						Message.error('请从首页点击报名')
						next('/home')
					}
				}
      },
      {
        path: '/enrollCategory',
        component: () => import('@/views/enrollCategory'),
        meta: { title: '报名入口 - 选择报名类别' },
				beforeEnter: (to, from, next) => {
					// 检查是否有入口id
					if (store.state.entry1.id && store.state.entry2.id) {
						next()
					} else {
						Message.error('请从首页点击报名')
						next('/home')
					}
				}
      },
			{
			  path: '/enrollSchool',
			  component: () => import('@/views/enrollSchool/index'),
			  meta: { title: '学校列表' },
				beforeEnter: (to, from, next) => {
					// 3个入口id必须存在
					if (store.state.entry1.id && store.state.entry2.id && store.state.entry3.setupId) {
						next()
					} else {
						Message.error('请从首页点击报名')
						next('/home')
					}
				}
			},
			{
			  path: '/schoolDetail',
			  component: () => import('@/views/enrollSchool/detail'),
			  meta: { title: '学校风采' }
			},
			{
			  path: '/adForm',
			  component: () => import('@/views/adForm/index'),
			  meta: { title: '报名' },
				beforeEnter: (to, from, next) => {
					// 3个入口id和学校必须存在
					if (store.state.entry1.id && store.state.entry2.id && store.state.entry3.setupId && store.state.schoolDetail.id) {
						next()
					} else {
						Message.error('请从首页点击报名')
						next('/home')
					}
				}
			},
			{
			  path: '/adFormEdit',
			  component: () => import('@/views/adForm/edit'),
			  meta: { title: '报名信息修改' },
				beforeEnter: (to, from, next) => {
					// 查询结果必须存在
					if (store.state.applyQryResult) {
						next()
					} else {
						next('/admissionInquiry')
					}
				}
			},
			{
			  path: '/adFormDetail',
			  component: () => import('@/views/adForm/detail'),
			  meta: { title: '报名详情' },
				beforeEnter: (to, from, next) => {
					// 查询结果必须存在
					if (store.state.applyQryResult) {
						next()
					} else {
						next('/admissionInquiry')
					}
				}
			},
    ]
  },
  {
    path: '/404',//匹配不到路由表中的路由时，路由重定向
    component: () => import('@/views/error/404')
  },
  {
    path: '/:pathMatch(.*)*',//匹配不到路由表中的路由时，路由重定向
    redirect: '/404'
  }
]

const router = new VueRouter({
  mode: 'hash',
	base: baseUrl,
  routes
})

//解决路由重复
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err)
}


export default router
