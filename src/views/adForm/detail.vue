<template>
	<div class="section ad-form-detail">
		<div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>报名详情</span>
		</div>
		<el-skeleton
			:loading="loadingAdForm"
			animated
		>
			<template slot="default">
				<el-alert 
					style="margin-bottom: 10px;"
					type="success"
					effect="dark"
					:title="`报名学校：${ adSchool }`"
					:closable="false">
				</el-alert>
					<!-- 普通字段 -->
					<dl v-for="item, idx in normalForm" :key="item.typeConfigId" class="form-group">
						<dt class="f-group-title">{{ item.infoName }}</dt>
						<dd class="f-group-detail">
							<div>
								<ul class="f-g-d-info-list">
									<li v-for="fi, fidx in item._normalItem" :key="fidx" class="f-g-d-item normal-item">
										<div class="desc-txt">{{ fi.fieldName }}：</div>
										<div class="res-txt" :item-config.sync="fi" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</div>
										<div class="res-txt"  v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
											<normal-select-ex :item-config.sync="fi"></normal-select-ex>
										</div>
									</li>
								</ul>
							</div>
							<div>
								<ul class="f-g-d-info-list">
									<li v-for="fi, fidx in item._imgItem" :key="fidx" class="f-g-d-item img-item">
										<normal-img-ex :item-config.sync="fi"></normal-img-ex>
									</li>
								</ul>
							</div>
						</dd>
					</dl>
					<!-- 双胞胎 -->
					<dl class="form-group" v-if="siblings.list.length > 0">
						<dt class="f-group-title">双胞胎信息</dt>
						<dd class="f-group-detail">
							<ul class="sib-list">
								<li class="sib-item" v-for="si, sIdx in siblings.list" :key="si.typeConfigId">
									 <el-divider content-position="left">双胞胎{{ sIdx + 1 }}</el-divider>
									<div>
										<ul class="f-g-d-info-list">
											<li v-for="fi, fidx in si._normalItem" :key="fidx" class="f-g-d-item normal-item">
												<div class="desc-txt">{{ fi.fieldName }}：</div>
												<div class="res-txt" :item-config.sync="fi" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</div>
												<div class="res-txt"  v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
													<normal-select-ex :item-config.sync="fi"></normal-select-ex>
												</div>
											</li>
										</ul>
									</div>
									<div>
										<ul class="f-g-d-info-list">
											<li v-for="fi, fidx in si._imgItem" :key="fidx" class="f-g-d-item img-item">
												<normal-img-ex :item-config.sync="fi"></normal-img-ex>
											</li>
										</ul>
									</div>
								</li>
							</ul>
						</dd>
					</dl>
					<!-- 房产 -->
					<template v-if="propertyForm.list.length > 0">
						<dl v-for="ppItem, ppIdx in propertyForm.list" :key="ppItem.typeConfigId" class="form-group">
							<dt class="f-group-title f-group-title-pp">
								<span>房产信息</span>
								<span>{{ propertyForm.list[0].infoName }}</span>
							</dt>
							<dd class="f-group-detail">
								<div>
									<ul class="f-g-d-info-list">
										<li v-for="fi, fidx in ppItem._normalItem" :key="fidx" class="f-g-d-item normal-item">
											<div class="desc-txt">{{ fi.fieldName }}：</div>
											<div class="res-txt" :item-config.sync="fi" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</div>
											<div class="res-txt"  v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
												<normal-select-ex :item-config.sync="fi"></normal-select-ex>
											</div>
										</li>
									</ul>
								</div>
								<div>
									<ul class="f-g-d-info-list">
										<li v-for="fi, fidx in ppItem._imgItem" :key="fidx" class="f-g-d-item img-item">
											<normal-img-ex :item-config.sync="fi"></normal-img-ex>
										</li>
									</ul>
								</div>
							</dd>
						</dl>
					</template>
					<template v-if="others.list.length > 0">
						<dl v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId" class="form-group">
							<dt class="f-group-title">其他材料证明</dt>
							<dd class="f-group-detail">
								<div>
									<ul class="f-g-d-info-list">
										<li v-for="fi, fidx in oItem._normalItem" :key="fidx" class="f-g-d-item normal-item">
											<div class="desc-txt">{{ fi.fieldName }}：</div>
											<div class="res-txt" :item-config.sync="fi" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</div>
											<div class="res-txt"  v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
												<normal-select-ex :item-config.sync="fi"></normal-select-ex>
											</div>
										</li>
									</ul>
								</div>
								<ul class="f-g-d-info-list">
									<li v-for="fi, fidx in oItem._imgItem" :key="fidx" class="f-g-d-item img-item">
										<normal-img-ex :item-config.sync="fi"></normal-img-ex>
									</li>
								</ul>
							</dd>
						</dl>
					</template>
				<div style="display: flex; justify-content: center; align-items: center;">
					<el-button @click="_goBack()">返回</el-button>
					<template v-if="showModify">
						<el-button type="primary" @click="editAd">修改报名</el-button>
						<el-button type="danger" @click="initResetConfirm">重新报名</el-button>
					</template>
				</div>
			</template>
		</el-skeleton>
		<el-dialog
			title="修改报名"
			width="500px"
			:visible.sync="editConfirm.show"
			:close-on-click-modal="false"
			center
		>
			<p>确定修改当前报名信息吗？当前修改报名次数仅剩：<span style="color: #F56C6C;">{{ editConfirm.remain }}次</span></p>
			<div slot="footer">
				<el-button @click="editConfirm.show = false">取消</el-button>
				<el-button type="primary" @click="go2AdEdit">确定</el-button>
			</div>
		</el-dialog>
		<!--  -->
		<el-dialog
			title="重新报名"
			:visible.sync="resetConfirm.show"
			:close-on-click-modal="false"
			width="500px"
			center
			@closed="closeResetConfirm"
		>
			<p>确定删除学生报名数据吗？删除以后请尽快在报名时期间重新报名，否则该学生可能导致无法正常入学，当前修改报名次数仅剩：<span style="color: #F56C6C;">{{ resetConfirm.remain }}次</span>，操作不可逆，请谨慎操作。</p>
			<div slot="footer">
				<el-button @click="closeResetConfirm">取消</el-button>
				<el-button type="primary" :disabled="resetConfirm.confirmBtnDisable" @click="delAndResetAd">{{ resetConfirm.confirmBtnTxt }}</el-button>
				<timer ref="resetAdTimer" :total="resetConfirm.timerTotalSec" @start="timerStart" @ongoing="timerOngoing" @end="timerEnd"></timer>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { adFormDetail, adRemainEditCount, isInAdTimeRange2, delAd } from "@/api/index.js"
import NormalSelectEx from "@/components/Exhibition/NormalSelectEx"
import NormalImgEx from "@/components/Exhibition/NormalImgEx"
import Timer from "@/components/timer.vue"
export default {
	components: {
		NormalSelectEx,
		NormalImgEx,
		Timer
	},
	data() {
		return {
			// 加载中
			loadingAdForm: true,
			// 录取查询来的报名信息
			applyRes: this.$store.state.applyQryResult,
			// 报名人
			stuId: '',
			// 报名的学校
			adSchool: '',
			// 整个页面循环用的list
			originList: [],
			// 非房产非双胞胎的普通字段
			normalForm: [],
			// 房产
			propertyForm: {
				list: []
			},
			// 多胞胎
			siblings: {
				list: []
			},
			// 其他补充信息
			others: {
				list: []
			},
			// 学生身份证
			stuIdCard: '',
			// 是否显示修改报名
			showModify: false,
			// 修改信息弹窗
			editConfirm: {
				show: false,
				remain: '-'
			},
			// 重新填报弹窗
			resetConfirm: {
				show: false,
				// 剩余次数
				remain: '-',
				// 倒计时时长，单位秒
				timerTotalSec: 3,
				// 默认禁用确定按钮
				confirmBtnDisable: true,
				// 确定按钮txt
				confirmBtnTxt: ''
			}
		}
	},
	created() {
		this.stuId = this.applyRes.studentId
		this.adSchool = this.applyRes.enrollSchoolName
		this.showModify = this.$store.state.showModifyTp == 2
		this.getAdDetail()
	},
	methods: {
		// 获取次数
		async getRemainCount() {
			// 剩余可修改次数
			let count = await adRemainEditCount({ key: this.stuIdCard })
			return count
		},
		// 获取报名字段
		getAdDetail() {
			adFormDetail({
				key: this.stuId
			}).then(res => {
				// 按typeConfigId从小到大排序
				let resCopy = JSON.parse(JSON.stringify(res.enrollMiddleFieldVos)).sort((a, b) => a.typeConfigId - b.typeConfigId)
				// 取基础信息里的学生身份证
				this.stuIdCard = resCopy.find(v => v.typeConfigId == 1).leafFieldInfos.find(v => v.fieldId == 3).fieldValue
				// 分类
				this.originList = resCopy.map(this.separateImgAndNormal)

				// 处理经商、务工、居住证信息：哪个有数据就显示哪个（三选一）
				const businessInfo = this.originList.find(v => v.typeConfigId == 5) // 经商信息
				const workInfo = this.originList.find(v => v.typeConfigId == 6) // 务工信息
				const residenceInfo = this.originList.find(v => v.typeConfigId == 7) // 居住证信息

				// 先获取基础信息模块（排除经商、务工、居住证）
				this.normalForm = this.originList.filter(v => v.typeConfigId < 8 && v.typeConfigId != 3 && v.typeConfigId != 5 && v.typeConfigId != 6 && v.typeConfigId != 7)

				// 然后添加有数据的经商、务工、居住证信息（按优先级：经商 > 务工 > 居住证）
				if (businessInfo && this.hasValidData(businessInfo)) {
					this.normalForm.push(businessInfo)
				} else if (workInfo && this.hasValidData(workInfo)) {
					this.normalForm.push(workInfo)
				}
        if (residenceInfo && this.hasValidData(residenceInfo)) {
					this.normalForm.push(residenceInfo)
				}

				// 房产字段：typeConfigId >= 8但小于19
				this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 &&  v.typeConfigId < 18)
				// 双胞胎字段：typeConfigId为3和19
				this.siblings.list = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
				// 其他补充信息
				this.others.list = this.originList.filter(v => v.typeConfigId == 18)
				// 加载完成
				this.loadingAdForm = false
			})
		},
		// 字段通用处理：区分图片与非图片字段
		separateImgAndNormal(item) {
			// 非图片字段
			item._normalItem = item.leafFieldInfos.filter(fi => fi.type == 1)
			// 图片字段，并且有值的
			item._imgItem = item.leafFieldInfos.filter(fi => fi.type == 2 && fi.fieldValue)
			return item
		},
		// 检查字段组是否有有效数据
		hasValidData(item) {
			if (!item || !item.leafFieldInfos) return false
			// 检查是否有任何字段有值
			return item.leafFieldInfos.some(field => {
				return field.fieldValue && field.fieldValue.trim() !== ''
			})
		},
		// 修改报名
		async editAd() {
			let count = await this.getRemainCount()
			this.editConfirm.remain = count
			this.editConfirm.show = true
		},
		// 确定修改报名
		go2AdEdit() {
			this.$router.push('/adFormEdit')
		},
		// 倒计时开始
		timerStart() {
			this.resetConfirm.confirmBtnTxt = `确定（${ this.resetConfirm.timerTotalSec }秒）`
		},
		// 倒计时进行中
		timerOngoing(remain) {
			this.resetConfirm.confirmBtnTxt = `确定（${ remain }秒）`
		},
		// 倒计时结束
		timerEnd() {
			this.resetConfirm.confirmBtnTxt = `确定`
			this.resetConfirm.confirmBtnDisable = false
		},
		// 取消/关闭重新报名弹窗
		closeResetConfirm() {
			this.$refs['resetAdTimer']._endTimer()
			this.resetConfirm.show = false
		},
		// 开启重新报名弹窗，初始化定时器
		async initResetConfirm() {
			let count = await this.getRemainCount()
			this.resetConfirm.remain = count
			this.resetConfirm.show = true
			this.resetConfirm.confirmBtnDisable = true
			this.$refs['resetAdTimer']._startTimer()
		},
		// 确定删除报名信息
		delAndResetAd() {
			delAd({
				key: this.$store.state.user.userId
			}).then(res => {
				this.$message.success('报名信息已删除，请重新报名')
				this.$router.replace('/home')
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.ad-form-detail {
	background-color: #FFF;
	padding: 10px;
	.common-title {
		margin-bottom: 10px;
	}
}
.form-group {
	margin: 0;
	.f-group-title {
		margin-bottom: 20px;
		padding: 0 15px;
		line-height: 46px;
		border-radius: 5px;
		background-color: #F1F1F1;
	}
	.f-group-title-pp {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.f-group-detail {
		margin: 0;
		margin-bottom: 30px;
		.f-g-d-info-list, .f-g-d-img-list {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;
		}
		.f-g-d-info-list {
			padding: 0 10px;
		}
		.normal-item {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex: 0 0 33.3333%;
			margin-bottom: 10px;
		}
		.img-item {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			flex: 0 0 14.2857%;
			margin: 15px 0;
			.desc-txt {
				margin-top: 10px;
			}
		}
	}
}
</style>