<template>
	<div class="section ad-form">
		<div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>报名</span>
		</div>
		<el-skeleton
			:loading="loadingAdForm"
			animated
		>
			<template slot="default">
				<el-alert
					style="margin-bottom: 10px;"
					type="success"
					effect="dark"
					:title="`当前报名学校：${ adSchool.deptName }`"
					:closable="false">
				</el-alert>
				<el-form ref='form' :model="form" :rules="rules" size="small" :inline="true" label-width="170px">
					<!-- 普通字段 -->
					<dl v-for="item, idx in normalForm" :key="item.typeConfigId" class="form-group">
						<dt class="f-group-title">{{ item.infoName }}</dt>
						<dd class="f-group-detail">
							<div>
								<template v-for="fi, fidx in item._normalItem">
									<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
										<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
										<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>

                  </el-form-item>
                  <div v-if="fi.fieldId == 125" class="field-tipss">
                    注：请填写毕业小学全称
                  </div>
								</template>
							</div>
							<div class="f-g-d-img-list">
								<template v-for="fi, fidx in item._imgItem">
									<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
										<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
									</el-form-item>
								</template>
							</div>
							<div class="f-group-tips">
								<div class="tips-item" v-if="item.typeConfigId == 2">
									<p>注：</p>
									<ol>
										<li>含*项均为必填项，请核查后仔细填写，确保无误</li>
										<li>上传的照片大小应小于6M</li>
									</ol>
								</div>
							</div>
						</dd>
					</dl>
					<!-- 双胞胎 -->
					<dl class="form-group" v-if="siblings.allList.length > 0">
						<dt class="f-group-title">
							<el-checkbox v-model="siblings.isHaveSib" @change="initOrHideSib">双胞胎信息</el-checkbox>
						</dt>
						<dd class="f-group-detail" v-show="siblings.isHaveSib">
							<ul class="sib-list">
								<li class="sib-item" v-for="si, sIdx in siblings.list" :key="si.typeConfigId">
									 <el-divider content-position="left">双胞胎{{ sIdx + 1 }}</el-divider>
									<div>
										<template v-for="fi, fidx in si._normalItem">
											<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
												<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
												<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
											</el-form-item>
										</template>
									</div>
									<div class="f-g-d-img-list">
										<template v-for="fi, fidx in si._imgItem">
											<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
												<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
											</el-form-item>
										</template>
									</div>
								</li>
							</ul>
							<div class="sib-actions">
								<el-button size="small" type="primary" @click="sibAdd" v-if="siblings.list.length == 1">添加</el-button>
								<el-button size="small" type="danger" @click="sibDel" v-if="siblings.list.length == 2">删除</el-button>
							</div>
						</dd>
					</dl>
					<!-- 随迁 -->
					<dl class="form-group" v-if="sq.isSQ">
						<dt class="f-group-title both-side">
							<span>{{ sq.tpCn }}</span>
							<el-radio-group v-model="sq.tpCn" @input="sqTpChange" size="mini">
								<el-radio-button :key="sqRadioItem.typeConfigId" v-for="sqRadioItem in sq.allList" :label="sqRadioItem.infoName"></el-radio-button>
							</el-radio-group>
						</dt>
						<dd class="f-group-detail">
							<div v-for="sqItem in sq.list" :key="sqItem.typeConfigId">
								<div>
									<template v-for="fi, fidx in sqItem._normalItem">
										<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
											<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
											<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
										</el-form-item>
									</template>
								</div>
								<div class="f-g-d-img-list">
									<template v-for="fi, fidx in sqItem._imgItem">
										<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
											<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
										</el-form-item>
									</template>
								</div>
                <div class="f-group-tips" v-if="sq.tpCn==='务工信息'">
                  <div class="tips-item" >
                    <p>注：养老保险、失业保险和住房公积金选一项上传即可</p>
<!--                    <ol>-->
<!--                      <li>含*项均为必填项，请核查后仔细填写，确保无误</li>-->
<!--                      <li>上传的照片大小应小于6M</li>-->
<!--                    </ol>-->
                  </div>
                </div>
							</div>
						</dd>
					</dl>
					<!-- 房产 -->
					<dl class="form-group" v-if="propertyForm.list.length > 0">
						<dt class="f-group-title">房产信息</dt>
						<dd class="f-group-detail">
							<el-form-item label="房产类型">
								<el-select style="width: 220px" size="small" v-model="propertyForm.tp" @change="ppTabChange">
									<el-option v-for="ppItem in propertyForm.list" :value="ppItem.typeConfigId" :label="ppItem.infoName" :key="ppItem.typeConfigId"></el-option>
								</el-select>
							</el-form-item>
							<div v-for="ppItem in propertyForm.curList" :key="ppItem.typeConfigId">
								<div>
									<template v-for="fi, fidx in ppItem._normalItem">
										<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
											<normal-input :ref="fi.fieldId" :item-config.sync="fi" :isNumber="propertyForm.tp == 9 && fi.fieldId == 71" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
											<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
										</el-form-item>
										<!-- 为不动产权证书的房屋坐落地址添加提示 -->
										<div v-if="propertyForm.tp == 9 && fi.fieldId == 148" class="field-tips">
                      注：请输入不动产权证第X号中的数字部分 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：地址从小区名称开始填写
										</div>

                    <div v-if="propertyForm.tp == 8 && fi.fieldId == 145" class="field-tips">
                      注：请输入房产证编号中'X字第'后面的数字部分 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：地址从小区名称开始填写
                    </div>

									</template>
								</div>
								<div class="f-g-d-img-list">
									<template v-for="fi, fidx in ppItem._imgItem">
										<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
											<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
										</el-form-item>
									</template>
								</div>
							</div>
						</dd>
					</dl>
					<template v-if="others.list.length > 0">
						<dl v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId" class="form-group">
							<dt class="f-group-title">其他材料证明</dt>
							<dd class="f-group-detail">
								<div>
									<template v-for="fi, fidx in oItem._normalItem">
										<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
											<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
											<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
										</el-form-item>
									</template>
								</div>
								<template v-for="fi, fidx in oItem._imgItem">
									<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
										<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
									</el-form-item>
								</template>
							</dd>
						</dl>
					</template>
				</el-form>
				<div style="display: flex; justify-content: center; align-items: center;">
					<el-button @click="_goBack()">返回</el-button>
					<el-button type="primary" :loading="submitDisable" @click="submitAdForm">提交</el-button>
				</div>
			</template>
		</el-skeleton>
	</div>
</template>

<script>
import { rulesList, triggerTpByCode } from "@/utils/dictionary"
import { qryAdFormByEntry, submitAd, submitAdSecond, getNextPath } from "@/api/index.js"
import NormalInput from "@/components/Form/NormalInput"
import NormalSelect from "@/components/Form/NormalSelect"
import NormalImgUpload from "@/components/Form/NormalImgUpload"
import { idCardValidator, mobileValidator } from "@/utils/validator.js"
import { LoopFn } from "@/mixins/loopFn"
export default {
	components: {
		NormalInput,
		NormalSelect,
		NormalImgUpload
	},
	mixins: [ LoopFn ],
	data() {
		return {
			// 报名类别
			entryId: this.$store.state.entry3.setupId,
			// 要报名的学校
			adSchool: this.$store.state.schoolDetail
		}
	},
	created() {
		// 是否随迁类型
		this.sq.isSQ = this.sq.idList.some(v => v == this.entryId)
		this.getAdForm()
	},
	methods: {
		// 获取报名字段
		getAdForm() {
			qryAdFormByEntry({
				key: this.entryId
			}).then(res => {
				// 按typeConfigId从小到大排序
				let resCopy = JSON.parse(JSON.stringify(res)).sort((a, b) => a.typeConfigId - b.typeConfigId)
				// 分类
				this.originList = resCopy.map(this.separateImgAndNormal)
				// 是随迁
				if (this.sq.isSQ) {
					// 排除经商5、务工6、居住证7，改为三选一
					let normalIdList = [1, 2, 4].map(v => `${ v }`)
					this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
					// 获取经商、务工、居住证信息，并按居住证、经商、务工的顺序排列
					this.sq.allList = this.originList.filter(v => v.typeConfigId == 5 || v.typeConfigId == 6 || v.typeConfigId == 7)
						.sort((a, b) => {
							// 居住证(7)排第一，经商(5)排第二，务工(6)排第三
							const order = { 7: 1, 5: 2, 6: 3 }
							return order[a.typeConfigId] - order[b.typeConfigId]
						})
					// 如果配置了经商、务工、居住证
					if (this.sq.allList.length > 0) {
						// 默认选择第一个选项（现在居住证排在第一位）
						this.sq.tp = this.sq.allList[0].typeConfigId
						this.sq.tpCn = this.sq.allList[0].infoName
						this.sqTpChange(this.sq.tpCn)
					}
				} else {
					let normalIdList = [1, 2, 4, 5, 6, 7].map(v => `${ v }`)
					this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
				}
				// 房产字段：typeConfigId >= 8但小于18
				this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 &&  v.typeConfigId < 18)
				// 如果有房产
				if (this.propertyForm.list.length > 0) {
					// 房产默认选中第1个tab
					this.propertyForm.tp = this.propertyForm.list[0].typeConfigId
					this.propertyForm.lastTp = this.propertyForm.tp
					this.propertyForm.curList = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
					// 添加第1个tab的验证规则
					this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp).map(this.addValidRules)
				}
				// 双胞胎字段：typeConfigId为3和19
				this.siblings.allList = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
				// 其他补充信息
				this.others.list = this.originList.filter(v => v.typeConfigId == 18)
				if (this.others.list.length > 0) {
					this.others.list.map(this.addValidRules)
				}
				// 加载完成
				this.loadingAdForm = false
			})
		},
		// 提交
		submitAdForm() {
			this.submitDisable = true
			// 默认第1次报名
			let params = {
				setUpSaveIds: this.entryId,
				enrollSchoolId: this.adSchool.id,
				enrollSchoolName: this.adSchool.deptName,
				userId: this.$store.state.user.userId,
				enrollMiddleFieldFormList: this.originList,
				houseType: this.propertyForm.tp,
				followWorkType: ''
			}
			// 没选双胞胎就去掉上传字段
			if (!this.siblings.isHaveSib) {
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 3 && v.typeConfigId != 19)
			} else if (this.siblings.list.length == 1) {
				// 只有一条时删掉id是19的
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 19)
			}
			// 是随迁传入选中的随迁类型
			if (this.sq.isSQ) {
				params.followWorkType = this.sq.tp
			}
			this.$refs['form'].validate(async valid => {
				if (valid) {
					// 查询当前学生是否报过名
					let isStuAd = await getNextPath({ key: this.$store.state.user.userId })
					if (isStuAd == 0) {
						// 走新增报名
						submitAd(params).then(res => {
							this.$message.success('报名成功')
							this.$router.replace('/home')
							this.submitDisable = false
						}).catch(err => {
							this.submitDisable = false
						})
					} else {
						// 否则走修改报名接口
						submitAdSecond(params).then(res => {
							this.$message.success('修改报名成功')
							this.$router.replace('/home')
							this.submitDisable = false
						}).catch(err => {
							this.submitDisable = false
						})
					}
				} else {
					this.submitDisable = false
					return false
				}
			})
		}
	}
}

</script>

<style scoped>
.field-tips {
  color: red;
  padding-left: 65px;
  margin-top: -5px;
  margin-bottom: 10px;
}
.field-tipss {
  color: red;
  padding-left: 489px;
}
</style>
