export default {
    data() {
        return {
            tableData: {
							list: [],
							pageNumber: 1,
							pageSize: 10,
							total: 0
            },
            search: {
							pageNumber: 1,
							pageSize: 10
            },
            routePath: this.$route.fullPath
        }
    },
		computed: {
			total() {
					return this.tableData && this.tableData.total ? Number(this.tableData.total) : 0
			}
		},
    methods: {
        getTableIndex(index) {
            return (this.search.pageNumber - 1) * this.search.pageSize + index + 1
        },
        getTableData() {
            console.error("请实现 getTableData 方法")
        },
        searchSubmit() {
					this.search.pageNumber = 1
          this.getTableData()
        },
        handleSizeChange(size) {
            this.search.pageSize = size
            this.search.pageNumber = 1
            this.getTableData()
        },
        handleCurrentChange(page) {
            this.search.pageNumber = page
            this.getTableData()
        }
    },
    destroyed() {
        window.removeEventListener('beforeunload', this.handleBeforeUnload)
    }
}
