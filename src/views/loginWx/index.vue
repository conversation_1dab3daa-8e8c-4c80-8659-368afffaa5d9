<template>
  <div class="login">
    <div class="login-title">{{ systemName }}</div>
    <div class="login-section">
      <div class="login-box">
        <div class="login-box-title">
          <!-- <el-button icon="el-icon-back" @click="back" size="small"
            >返回</el-button
          > -->
          <span>微信扫码登录</span>
        </div>
        <div class="login-box-main" v-loading="showLoading">
          <div id="sd-wechat-qrcode"></div>
        </div>
      </div>
    </div>
    <Footer></Footer>
  </div>
</template>

<script>
import { showLoginForm } from "@/views/loginWx/wxLoginForm";
import Footer from "@/views/layout/components/Footer";
import { baseUrl } from "@/settings.js";
import { areaList } from "@/utils/dictionary.js"
import { getSystemName } from "@/api"
export default {
  components: { Footer },
  data() {
    return {
      showLoading: true,
      systemName: ``,
    };
  },
  async created() {
    this.$store.commit("resetState");
    // this.systemName = `${ areaList.find(v => v.id == this.$store.state.deptCode).val }教育入学一件事服务平台`
		this.getSystemName()
    setTimeout(() => (this.showLoading = false), 1000);
    const token = this.$store.state.token;
    if (token) {
      await this.$router.push("/home");
    } else {
      this.showWechatForm();
    }
  },
  methods: {
    showWechatForm() {
      const cssText =
        "LmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5Om5vbmU7fQouaW1wb3dlckJveCAucXJjb2RlIHtoZWlnaHQ6MjAwcHg7d2lkdGg6MjAwcHg7dXNlci1zZWxlY3Q6bm9uZTt9Ci5pbXBvd2VyQm94IC5pbmZve3dpZHRoOiBhdXRvO30KLmltcG93ZXJCb3ggLmluZm8gLnN0YXR1c19icm93c2VyIHA6bnRoLWNoaWxkKDIpe2Rpc3BsYXk6IG5vbmU7fQoudHBsX2Zvcl9pZnJhbWUge3VzZXItc2VsZWN0Om5vbmU7fQ==";
      showLoginForm("sd-wechat-qrcode", `${baseUrl}/#/auth`, cssText);
    },
		// 系统名称
    getSystemName() {
      getSystemName({}).then(res => {
        this.systemName = res
      })
    },
    // 返回
    // back() {
    //   this.$router.push('back');
    // },
  },
};
</script>

<style scoped>
.login {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #eff8ff;
}
.login .login-title {
  position: absolute;
  left: 50%;
  top: 75px;
  transform: translateX(-50%);
  font-size: 36px;
  color: #000;
  letter-spacing: 2px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.login .login-section {
  width: 100%;
  height: 640px;
  margin-top: 160px;
  background: url("../../assets/img/login-bg.jpg");
  border-top: 1px solid #fff;
  border-bottom: 1px solid #fff;
}
.login .login-box {
  position: absolute;
  top: 240px;
  left: 50%;
  width: 454px;
  height: 425px;
  background-color: #ffffff;
  border-radius: 10px;
  margin-left: -227px;
  box-shadow: 0 0 10px rgb(219, 218, 218);
}
.login-box-title {
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 0 45px;
  text-align: center;
  border-bottom: 1px solid #dcdfe6;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: relative;
}
.login-box-title span {
  display: inline-block;
  height: 60px;
  color: #666;
  font-size: 19px;
  font-weight: bold;
  letter-spacing: 2px;
}
.login-box-main {
  font-size: 20px;
  display: flex;
  justify-content: center;
}
#sd-wechat-qrcode .impowerBox .info {
  width: auto !important;
}
.footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  background-color: #eff8ff;
}
</style>
