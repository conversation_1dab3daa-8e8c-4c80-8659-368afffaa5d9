import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import './assets/css/global.scss'
import './permission.js'

// 公共返回方法
import { GoBack } from './mixins/GoBack.js'
Vue.mixin(GoBack)

Vue.prototype.$pageSizes = [10, 20, 30, 40, 50]

Vue.use(ElementUI)
Vue.config.productionTip = false
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
