import axios from 'axios'
import store from '@/store'
import router from '@/router'
import NProgress from 'nprogress'
import "nprogress/nprogress.css";
import { Loading, Message } from 'element-ui'
import settings from "../settings";

const request = axios.create({
    baseURL: process.env.VUE_APP_BASE_API,
    timeout: 20000 //请求超时 5000毫秒
})

//请求拦截器
request.interceptors.request.use(config => {
    const token = store.state.token
    config.headers.Authorization = token
		config.headers.state = store.state.state4Headers
		config.headers['client-id'] = 'pc'
    //打开加载窗口
    //loading.open()
    NProgress.start()
    return config
}, error => {
    //关闭加载窗口
    //loading.close()
    NProgress.done()
    return Promise.reject(error)
})

//响应拦截器
request.interceptors.response.use( res => {
    //关闭加载窗口
    //loading.close()
    NProgress.done()
    if(res.data.status && res.data.status != 200) {
        Message({
            message: res.data.message || '请求异常',
            type: 'warning',
            duration: 3 * 1000
        })
    }
    return res.data.data
},
(error) => {
    //关闭加载窗口
    //loading.close()
    NProgress.done()
    const errorMessage = error.response.data.message||'当前访问人数过多，请稍后重试'
    Message({
        message: errorMessage,
        type: 'error',
        duration: 3 * 1000
    })
    //判断响应失败状态
    //异地登录或token失效
    console.log(error.response.data.status)
    if( error.response.data.status == 401 || error.response.data.status == 407) {
        //sessionStorage.clear()
        store.commit('resetState')
        router.replace('/loginWx')
    }
    return Promise.reject(error)
})


export default request //导出自定义创建的axios对象
