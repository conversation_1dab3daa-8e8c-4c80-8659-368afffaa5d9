<template>
	<div class="normal-input">
		<el-input 
			v-model.trim="modelData" 
			:placeholder="phTxt"  
			style="width: 220px;"
			:type="inputType"
			@input="handleInput"
			:class="{ 'number-input': isNumber }"
		></el-input>
	</div>
</template>

<script>
export default {
	name: 'normal-input',
	data() {
		return {
			modelData: '',
			phTxt: ''
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		},
		// 是否为数字输入框
		isNumber: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		inputType() {
			return this.isNumber ? 'number' : 'text'
		}
	},
	watch: {
		modelData(newV, oldV) {
			if (newV != oldV) {
				this.valChange(newV)
			}
		}
	},
	created() {
		this.phTxt = `请输入${ this.itemConfig.fieldName }`
		// 报名修改页面：如果有值，直接填充
		if (this.itemConfig.fieldValue) {
			this.modelData = this.itemConfig.fieldValue
		}
	},
	methods: {
		// 处理输入，如果是数字输入框则只允许数字
		handleInput(value) {
			if (this.isNumber) {
				// 只保留数字
				this.modelData = value.replace(/[^\d]/g, '')
			}
		},
		// emit
		valChange(data) {
			this.modelData = data
			this.$emit('value-change', {
				id: this.itemConfig.fieldId,
				val: data
			})
		}
	}
}
</script>

<style scoped>
/* 隐藏数字输入框的上下箭头 */
.number-input ::v-deep input[type="number"]::-webkit-outer-spin-button,
.number-input ::v-deep input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.number-input ::v-deep input[type="number"] {
  -moz-appearance: textfield;
}
</style>
