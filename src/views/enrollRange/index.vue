<template>
  <div class="section">
    <div class="common-title">招生范围</div>
    <div class="section-main">
      <el-card>
        <ul>
          <li>
            <span @click="goPrimary"
              >小学招生范围 <i class="el-icon-arrow-right"></i
            ></span>
          </li>
          <li>
            <span @click="goJunior"
              >初中招生范围 <i class="el-icon-arrow-right"></i
            ></span>
          </li>
        </ul>
      </el-card>
      <el-button
        class="common-btn"
        type="primary"
        plain
        size="small"
        @click="back"
        >返回</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  created() {},
  methods: {
    // 跳转小学招生范围
    goPrimary() {
      this.$router.push("/enrollRangePrimary");
    },
    // 跳转初中招生范围
    goJunior() {
      this.$router.push("/enrollRangeJunior");
    },
    // 返回
    back() {
      this.$router.push("/home");
    },
  },
};
</script>

<style scoped>
.section {
  background-image: linear-gradient(
    to bottom,
    rgba(232, 243, 252, 1) 5%,
    rgba(246, 251, 254, 1) 100%
  );
}
.section-main {
  padding: 0 40px;
}
.el-card {
  margin-top: 15px;
}
.el-card ul li {
  height: 60px;
  line-height: 60px;
  display: flex;
  justify-content: center;
  font-size: 16px;
  color: var(--color);
}
.el-card ul li span:hover {
  cursor: pointer;
}
.el-button {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}
</style>