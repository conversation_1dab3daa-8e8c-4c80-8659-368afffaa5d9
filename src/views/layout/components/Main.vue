<template>
    <div class="main">
        <!-- <keep-alive>
            <transition name="fade-transform" mode="out-in">
                <router-view v-if="this.$route.meta.keepAlive"></router-view>
            </transition>
        </keep-alive> -->
        <!-- v-if="!this.$route.meta.keepAlive" -->
        <transition name="fade-transform" mode="out-in">
            <router-view></router-view>
        </transition>
    </div>
</template>

<style scoped lang="scss">
.main {
	width: 1240px;
	margin: 40px auto 0;
	margin-bottom: 130px;
}
</style>