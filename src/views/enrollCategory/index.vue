<template>
  <div class="section">
    <div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>{{ entryName }}</span>
		</div>
    <div class="section-main">
      <div class="section-main-content">
        <div
          class="entrance3"
          v-for="item in entranceList"
          :key="item.id"
          @click="entranceNext(item)"
        >
          <div class="entrance-title">{{ item.idsName }}</div>
          <div class="entrance-content">{{ item.detail }}</div>
					<div class="entrance-time">报名开始时间：{{ item.startTime }}</div>
					<div class="entrance-time">报名结束时间：{{ item.endTime }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { 
	getSetupSaveDetail,
	isInAdTimeRange2,
	getNextPath
} from "@/api";
import { Entrance } from "@/mixins/EntranceTips"
export default {
	mixins: [ Entrance ],
  data() {
    return {
      entryName: this.$store.state.entry2.name,
			entryId: this.$store.state.entry2.id,
      entranceList: [],
    };
  },
  created() {
		this.$store.commit('setEntry3', '')
		this.$store.commit('setSchoolDetail', {})
    this.getSetupSaveDetail();
  },
  methods: {
    // 报名入口 - 查询类别
    getSetupSaveDetail() {
      getSetupSaveDetail({ key: this.entryId }).then((res) => {
        this.entranceList = this.sortEntranceList(res);
      });
    },
    // 排序报名类别列表
    sortEntranceList(list) {
      // 定义排序优先级
      const orderMap = {
        '第一批次:两统一适龄儿童报名入口': 1,
        '第二批次:户口房产不一致适龄儿童报名入口': 2,
        '第三批次:祖父母/外祖父母房产适龄儿童报名入口': 3,
        '第四批次:滦南有房产无户籍适龄儿童报名入口': 4,
        '第五批次:随迁及其他情况入学适龄儿童报名入口': 5,
        '随迁子女(滦南有房产)适龄儿童报名入口': 6,
        '其他适龄儿童报名入口': 7
      };
      
      // 对列表进行排序
      return [...list].sort((a, b) => {
        const orderA = orderMap[a.idsName] || 999; // 未定义的类型放最后
        const orderB = orderMap[b.idsName] || 999;
        return orderA - orderB;
      });
    },
    // 报名入口 - 选择类别
    async entranceNext(item) {
			let isStuAd = await getNextPath({ key: this.$store.state.user.userId })
			// 没报过名, 驳回重报时可以进入
			if (isStuAd == 0) {
				isInAdTimeRange2({
					key: item.setupId
				}).then(res => {
					if (!res) {
						this._entryNotInTime()
					} else {
						this.go2Next(item)
					}
				})
			} else if (isStuAd == 4) {
				// 直接进
				this.go2Next(item)
			} else {
				this.$message.warning('您已报名，修改信息请从录取查询处进入修改')
			}
    },
		// 跳转
		go2Next(item) {
			this.$store.commit('setEntry3', item)
			this.$router.push("/enrollSchool")
		}
  }
}
</script>

<style scoped lang="scss">
.section {
	background: #FFF;
}
.section-main-content {
	flex-wrap: wrap;
	align-items: flex-start;
}
.entrance3 {
  flex: 0 0 27%;
	margin-bottom: 40px;
	padding-bottom: 15px;
	border: 1px dotted #a5a5a5;
	border-radius: 4px;
  cursor: pointer;
	.entrance-title {
		padding: 15px 10px;
		text-align: center;
	  background: var(--color);
	  color: #fff;
	  border-radius: 4px 4px 0 0;
	}
	.entrance-content {
	  height: 290px;
		overflow-y: auto;
		margin-bottom: 15px;
	  padding: 15px;
		text-indent: 14px;
	  color: #000;
	}
	.entrance-time {
		text-align: center;
	}
}
</style>