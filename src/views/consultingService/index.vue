<template>
  <div class="section">
    <div class="common-title">咨询电话</div>
    <div class="field-tip">咨询电话工作时间 8:30-11:00、14:00-17:00;因招生季咨询量大，若拨打时未接通敬请谅解</div>
    <div class="section-main">
      <el-tabs v-model="curTab" type="border-card" @tab-click="tabChange">
        <el-tab-pane label="小学咨询电话" name="2">
          <el-table :data="tableData" border stripe>
            <el-table-column
              align="center"
              type="index"
              label="序号"
              width="60"
              fixed="left"
            >
            </el-table-column>
            <el-table-column
              align="center"
              label="学校名称"
              prop="deptName"
            ></el-table-column>
            <el-table-column align="center" label="学段" prop="period">
              <template slot-scope="scope">
                {{ ["", "幼儿园", "小学", "初中"][scope.row.period] }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="咨询电话"
              prop="mobile"
            ></el-table-column>
          </el-table>
          <!-- 分页 -->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.pageNumber"
            :page-size="queryParams.pageSize"
            layout="total, prev, pager, next"
            :total="total"
            background
          >
          </el-pagination>
        </el-tab-pane>
        <el-tab-pane label="初中咨询电话" name="3">
          <el-table :data="tableData" border stripe>
            <el-table-column
              align="center"
              type="index"
              label="序号"
              width="60"
              fixed="left"
            >
            </el-table-column>
            <el-table-column
              align="center"
              label="学校名称"
              prop="deptName"
            ></el-table-column>
            <el-table-column align="center" label="学段" prop="period">
              <template slot-scope="scope">
                {{ ["", "幼儿园", "小学", "初中"][scope.row.period] }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="咨询电话"
              prop="mobile"
            ></el-table-column>
          </el-table>
          <!-- 分页 -->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.pageNumber"
            :page-size="queryParams.pageSize"
            layout="total, prev, pager, next"
            :total="total"
            background
          >
          </el-pagination>
        </el-tab-pane>
        <el-tab-pane label="幼儿园咨询电话" name="1" v-if="showPreSchool">
          <el-table :data="tableData" border stripe>
            <el-table-column
              align="center"
              type="index"
              label="序号"
              width="60"
              fixed="left"
            >
            </el-table-column>
            <el-table-column
              align="center"
              label="学校名称"
              prop="deptName"
            ></el-table-column>
            <el-table-column align="center" label="学段" prop="period">
              <template slot-scope="scope">
                {{ ["", "幼儿园", "小学", "初中"][scope.row.period] }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="咨询电话"
              prop="mobile"
            ></el-table-column>
          </el-table>
          <!-- 分页 -->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.pageNumber"
            :page-size="queryParams.pageSize"
            layout="total, prev, pager, next"
            :total="total"
            background
          >
          </el-pagination>
        </el-tab-pane>
      </el-tabs>
      <el-button
        class="common-btn"
        type="primary"
        plain
        size="small"
        @click="back"
        >返回</el-button
      >
    </div>
  </div>
</template>

<script>
import {getPolicyList, deptPageList, deptPageLists} from "@/api";
export default {
  data() {
    return {
      curTab: "2",
      queryParams: {
        pageNumber: 1,
        pageSize: 10,
        nature:2,
        level: 3,
        period: "2",
        deptCode: this.$store.state.deptCode,
        type: "1",
      },
			// 涉县才有幼儿园
			showPreSchool: this.$store.state.deptCode == '130426',
      total: 0,
      tableData: [],
    };
  },
  created() {
    this.getData();
  },
  methods: {
    // 获取学校列表 - 小学
    getData() {
      deptPageLists(this.queryParams).then((res) => {
        this.tableData = res.records;
        this.total = Number(res.total);
      });
    },
    //每页显示条数
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getData();
    },

    //当前页码
    handleCurrentChange(val) {
      this.queryParams.pageNumber = val;
      this.getData();
    },
    tabChange() {
      this.queryParams.period = this.curTab
      this.getData()
    },
    // 返回
    back() {
      this.$router.push("/home");
    },
  },
};
</script>

<style scoped>
.section {
  width: 1240px;
  min-height: 579px;
  margin: 0 auto 20px;
  background-image: linear-gradient(
    to bottom,
    rgba(232, 243, 252, 1) 5%,
    rgba(246, 251, 254, 1) 100%
  );
}
.section-main {
  padding: 0 40px;
}
.el-tabs {
  margin-top: 15px;
  height: 680px;
}
.el-button {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}
.el-pagination {
  text-align: center;
  margin-top: 20px;
}
.field-tip {
  color: red;
  font-size: 15px;
  margin-bottom: 10px;
  padding-left: 296px;
}
</style>