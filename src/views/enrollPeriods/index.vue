<template>
  <div class="section">
    <div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>{{ entryName }}</span>
		</div>
    <div class="section-main">
      <div class="section-main-content">
        <div
          class="entrance"
          v-for="item in entranceList"
          :key="item.id"
          @click="entranceNext(item)"
          >{{ item.name }}</div
        >
      </div>
    </div>
  </div>
</template>

<script>
import { 
	getSetupSaveIds,
	isInAdTimeRange1,
	getNextPath
} from "@/api"
import { Entrance } from "@/mixins/EntranceTips"
export default {
	mixins: [ Entrance ],
  data() {
    return {
      entryName: this.$store.state.entry1.name,
			entryId: this.$store.state.entry1.id,
      entranceList: []
    };
  },
  created() {
		this.$store.commit('setEntry2', '')
		this.$store.commit('setEntry3', '')
		this.$store.commit('setSchoolDetail', {})
    this.getSetupSaveIds()
  },
  methods: {
    // 报名入口 - 查询学段
    getSetupSaveIds() {
      getSetupSaveIds({ key: this.entryId }).then((res) => {
        this.entranceList = res;
      });
    },
    // 报名入口 - 选择学段
    async entranceNext(item) {
			let isStuAd = await getNextPath({ key: this.$store.state.user.userId })
			// 没报过名, 驳回重报时可以进入
			if (isStuAd == 0) {
				isInAdTimeRange1().then(res => {
					if (item.id == 7) {
						if (res.villagesPrimarySchool) {
							this.go2Next(item)
						} else {
							this._entryNotInTime()
						}
					} else if (item.id == 8) {
						if (res.villagesJuniorSchool) {
							this.go2Next(item)
						} else {
							this._entryNotInTime()
						}
					} else if (item.id == 63) {
						if (res.urbanPrimarySchool) {
							this.go2Next(item)
						} else {
							this._entryNotInTime()
						}
					} else if (item.id == 64) {
						if (res.urbanJuniorSchool) {
							this.go2Next(item)
						} else {
							this._entryNotInTime()
						}
					}
				})
			} else if (isStuAd == 4) {
				// 直接进
				this.go2Next(item)
			} else {
				this.$message.warning('您已报名，修改信息请从录取查询处进入修改')
			}
    },
		// 跳转
		go2Next(item) {
			this.$store.commit('setEntry2', item)
			this.$router.push("/enrollCategory")
		}
  },
};
</script>

<style scoped>
.section {
	background: #FFF;
}
</style>
