<template>
  <div>
  </div>
</template>

<script>
export default {
  async created() {
    const token = this.$store.state.token;
    if (token) {
      await this.$router.push("/home");
    } else {
      const query = this.$route.query;
      if (query && query.state && query.code) {
          this.$store
            .dispatch("login", { code: query.code, state: query.state })
            .then((res) => {
              this.$message.success("登录成功");
							this.$store.commit('setLoginTp', 'wx')
              this.$router.push("/home");
            });
      }
    }
  }
}
</script>