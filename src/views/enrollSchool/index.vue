<template>
	<div class="section school-list">
		<div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>学校列表</span>
		</div>
		<el-table :data="tableData.records" border stripe style="width: 100%">
			<el-table-column
			    align="center"
			    label="序号"
			    width="60"
			    type="index"
			    fixed="left"
			  ></el-table-column>
			  <el-table-column
			    align="center"
			    label="学校名称"
			    prop="deptName"
			  ></el-table-column>
			  <el-table-column
			    align="center"
			    label="联系电话"
			    prop="mobile"
			  ></el-table-column>
				<el-table-column
				  align="center"
				  label="招生范围"
				  prop="area"
				></el-table-column>
				<el-table-column
				  align="center"
				  label="学校风采"
				  width="150"
				>
					<template slot-scope="{ row }">
						<el-button size="small" type="primary" @click="schoolDetail(row)">查看详情</el-button>
					</template>
				</el-table-column>
			  <el-table-column align="center" label="操作" width="150">
			    <template slot-scope="{ row }">
			      <el-button size="small" type="success" @click="go2AdForm(row)">我要报名</el-button>
			    </template>
			  </el-table-column>
			</el-table>
			<div class="page-container" v-if="total > 0">
			  <el-pagination
			    background
			    @size-change="handleSizeChange"
			    @current-change="handleCurrentChange"
			    :current-page.sync="search.pageNumber"
			    layout="total,prev, pager, next,sizes "
			    :page-sizes="$pageSizes"
			    :total="total"
			  >
			  </el-pagination>
			</div>
	</div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin"
import { deptPageList, qryFirst } from "@/api/index"
export default {
	mixins: [ TableMixin ],
	data() {
		return {
			search: {
			  keywords: "",
				// 乡镇/城区
			  nature: this.$store.state.entry1.id - 1,
				// 学段
			  period: "",
				deptCode: this.$store.state.deptCode,
			  type: 1,
				rejectSchoolId: '',
        setUpId:"",
			},
			list: []
		}
	},
	async created() {
		this.$store.commit('setSchoolDetail', {})
		let entry2 = this.$store.state.entry2.id
		// 城区幼儿园
		if (entry2 == 62) {
			this.search.period = 1
		} else if (entry2 == 7 || entry2 == 63) {
			// 7乡镇小学，63城区小学
			this.search.period = 2
		} else if (entry2 == 8 || entry2 == 64) {
			// 8乡镇初中，64城区初中
			this.search.period = 3
		}
    // console.log(this.$store.state.entry3.setupId)
    this.search.setUpId=this.$store.state.entry3.setupId

    this.getTableData()
	},
	methods: {
		// 列表
		getTableData() {
			deptPageList(this.search).then(data => {
				this.tableData = data
			})
		},
		// 风采
		schoolDetail(item) {
			this.$store.commit('setSchoolDetail', item)
			this.$router.push("/schoolDetail")
		},
		// 报名
		go2AdForm(item) {
			/* if (item._disabled) {
				this.$message.error('已被当前学校驳回报名，请选择其它学校')
			} else {
				
			} */
			this.$store.commit('setSchoolDetail', item)
			this.$router.push("/adForm")
		}
	}
}
</script>

<style scoped>
.school-list {
	background: none;
}
</style>