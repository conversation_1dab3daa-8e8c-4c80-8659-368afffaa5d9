<template>
  <div class="section">
    <div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>{{ info.title }}</span>
		</div>
    <div class="section-main">
      <div class="releaseDate">
        <span>发布时间：{{ info.createTime }}</span>
      </div>
      <div class="content" v-html="content"></div>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import { getPolicyDetail } from "@/api";
export default {
  data() {
    return {
      info: {},
			content: ''
    };
  },
  created() {
    this.getData(this.$route.query.id);
  },
  //路由跳转到政策公告列表时，启用keepAlive
  beforeRouteLeave(to, from, next) {
    if (to.path == "/policyAnnouncement") {
      to.meta.keepAlive = true;
    }
    next();
  },
  methods: {
    // 获取政策公告详情
    getData(id) {
      getPolicyDetail({ key: id }).then((res) => {
          this.info = res
					this.content = res.content
      });
    },

    // 返回
    back() {
      this.$router.push(store.state.currentNav);
    },
  },
};
</script>

<style scoped>
.section {
	background-image: linear-gradient(
	  to bottom,
	  rgba(232, 243, 252, 1) 5%,
	  rgba(246, 251, 254, 1) 100%
	);
}
.section-main {
  padding: 0 40px;
}
.section-main .releaseDate {
  width: 320px;
  margin: 0 auto;
  line-height: 38px;
	text-align: center;
}
.section-main .content {
  padding-bottom: 100px;
}
.section-main .content >>> img {
  max-width: 100%;
  height: auto;
}
</style>