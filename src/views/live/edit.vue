<template>
  <div class="section">
    <div class="common-title">
      <el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
      <span>居住证信息</span>
    </div>
    <div class="m-t-30">
      <el-tabs type="border-card" v-model="type">
        <el-tab-pane label="唐山各区户籍人员">
          <el-form ref="liveForm" :model="liveForm">
            <el-form-item label="" prop="liveCert">
              <imgUpload :keyName="`liveCert`" :keyCn="`居住证明`" :isRequire="true" :multiple="false" :limit="1"
                         @value-change="valueChange" :defaultValue="liveForm.liveCert"></imgUpload>
            </el-form-item>
          </el-form>
          <p class="live-label">注：拥有唐山各区户籍的人员只需传居住地居委会开具的居住证明</p>
        </el-tab-pane>
        <el-tab-pane label="唐山各区以外户籍人员">
          <el-form ref="form" :model="form" size="small" :inline="true" label-width="170px" :rules="rules">
            <el-form-item label="居住证编号" prop="code">
              <el-input v-model="form.code"></el-input>
            </el-form-item>
            <el-form-item label="居住证持有者" prop="holder">
              <el-input v-model="form.holder"></el-input>
            </el-form-item>
            <el-form-item label="与学生关系" prop="relation">
              <el-select v-model="form.relation">
                <el-option v-for="item in relationOption" :key="item.id" :label="item.val"
                           :value="item.val"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="居住证地址" prop="address">
              <el-input v-model="form.address"></el-input>
            </el-form-item>
            <el-form-item label="持有者身份证号" prop="idCard">
              <el-input v-model="form.idCard"></el-input>
            </el-form-item>
            <el-row>
              <el-form-item label="" prop="liveCert">
                <imgUpload :keyName="`liveCert`" :keyCn="`居住证`" :isRequire="true" :multiple="false" :limit="1"
                           @value-change="valueChange" :defaultValue="form.liveCert"></imgUpload>
              </el-form-item>
            </el-row>
            <p style="font-size: 10pt;color: red">注：唐山市各区以外户籍人员需提供居住证相关信息</p>
          </el-form>
        </el-tab-pane>

        <div class="b-justify-center m-t-90">
          <el-button type="primary" @click="next">下 一 步</el-button>
        </div>
      </el-tabs>

    </div>
  </div>
</template>

<script>
import imgUpload from "@/components/Form/imgUpload.vue";
import {liveRelationList} from "@/utils/dictionary";

export default {
  components: {
    imgUpload
  },
  data() {
    return {
      type: 0,
      form: {},
      liveForm: {},
      rules: {
        code: [
          {required: true, message: '请输入居住证编号', trigger: 'blur'}
        ],
        holder: [
          {required: true, message: '请输入居住证持有者', trigger: 'blur'}
        ],
        relation: [
          {required: true, message: '请输入与学生关系', trigger: 'blur'}
        ],
        address: [
          {required: true, message: '请输入居住证地址', trigger: 'blur'}
        ],
        idCard: [
          {required: true, message: '请输入持有者身份证号', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (!/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
                callback(new Error('请输入正确的身份证号'));
              } else {
                callback();
              }
            }
            , trigger: 'blur'
          }
        ]
      },
      relationOption: liveRelationList
    }
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let liveInfo = this.$store.state.liveInfo || {};
      let liveType = liveInfo.liveType;
      let liveOption = liveInfo.liveOption || {};

      // 确保 type 是字符串类型，并且有默认值
      this.type = liveType !== undefined ? String(liveType) : "0";
      
      // 根据类型初始化不同的表单，使用深拷贝避免引用问题
      if (Number(this.type) === 0) {
        this.liveForm = JSON.parse(JSON.stringify(liveOption));
        // 确保 liveCert 字段存在
        if (!this.liveForm.liveCert) {
          this.liveForm.liveCert = '';
        }
      } else {
        this.form = JSON.parse(JSON.stringify(liveOption));
        // 确保 liveCert 字段存在
        if (!this.form.liveCert) {
          this.form.liveCert = '';
        }
      }
    },
    async next() {
      const liveFlag = Number(this.type) === 0;
      const formRef = liveFlag ? this.$refs.liveForm : this.$refs.form;
      const formData = liveFlag ? this.liveForm : this.form;

      try {
        if (!formData.liveCert) {
          this.$message.error(liveFlag ? '请上传居住证明' : '请上传居住证');
          return;
        }
        const valid = await formRef.validate();
        if (!valid) return;

        const liveInfo = {
          // 转换为数字方便下个页面进行逻辑判断
          liveType: Number(this.type),
          liveOption: formData
        };
        this.$store.commit('setLiveInfo', liveInfo);
        await this.$router.push('/adFormEdit');
      } catch (error) {
        console.error('errMsg:', error);
      }
    },
    valueChange(param) {
      let {id, val} = param;
      if (Number(this.type) === 0) {
        // 确保 liveForm 已初始化
        if (!this.liveForm) this.liveForm = {};
        this.liveForm[id] = val;
      } else {
        // 确保 form 已初始化
        if (!this.form) this.form = {};
        this.form[id] = val;
      }
    },
  }
}
</script>

<style>
.live-label {
  text-align: center;
  margin-bottom: 10px;
  color: red;
  font-size: 10pt;
}
</style>