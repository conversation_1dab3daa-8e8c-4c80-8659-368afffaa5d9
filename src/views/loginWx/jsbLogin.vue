<template>
	<div class="jsb-login"></div>
</template>

<script>
import { provPltFmLogin, jsbLogin } from '@/api/index.js'
export default {
	data() {
		return {
			token: '',
			/*
				2种登录方式：
				1. 从sso.html点击去省平台，登录后地址栏传参token并跳回本页面。（省平台登录）
				2. 冀时办地址栏传参token与type=jsb，直接访问本页面，（冀时办登录）
			*/ 
			// 是否冀时办登录
			isJsb: false
		}
	},
	created() {
		this.$message.info('正在登录，请稍候...')
		let tokenStartIdx = location.href.indexOf('token=')
		this.token = location.href.substring(tokenStartIdx).replace('token=', '')
		// 是否冀时办登录
		this.isJsb = location.href.indexOf('type=jsb') > -1
		this.$store.commit("resetState")
		this.login()
	},
	methods: {
		login() {
			// 冀时办
			if (this.isJsb) {
				jsbLogin(this.token).then(res => {
					this.$store.commit('setLoginTp', 'jsb')
					this.afterLogin(res)
				})
			} else {
				// 省平台
				provPltFmLogin(this.token).then(res => {
					this.$store.commit('setLoginTp', 'prov')
					this.afterLogin(res)
				})
			}
		},
		// 登录后的统一操作
		afterLogin(res) {
			this.$store.commit('setToken', res.token)
			this.$store.commit('setUser', { userId: res.id, username: res.username, nickname: res.nickname })
			this.$store.commit('setState4Headers', res.state)
			this.$message.success("登录成功")
			this.$router.push('/home')
		}
	}
}
</script>
