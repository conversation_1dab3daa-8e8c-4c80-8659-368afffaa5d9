<template>
	<div class="section school-detail">
		<div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>学校详情</span>
		</div>
		<div class="section-main">
			<h2 class="s-nm">{{ schoolNm }}</h2>
			<p class="s-info">学校地址：{{ schoolAddr }}</p>
			<p class="s-info">学校电话：{{ schoolTel }}</p>
			<div v-html="schoolContent"></div>
		</div>
	</div>
</template>

<script>
import { schoolDetail } from "@/api/index"
export default {
	data() {
		return {
			id: this.$store.state.schoolDetail.id,
			schoolNm: '',
			schoolAddr: '',
			schoolTel: '',
			schoolContent: ''
		}
	},
	created() {
		this.getData()
	},
	methods: {
		// 获取详情
		getData() {
			schoolDetail({
				key: this.id
			}).then(res => {
				this.schoolNm = res.deptName
				this.schoolAddr = res.address
				this.schoolTel = res.mobile
				this.schoolContent = res.content
			})
		}
	}
}
</script>

<style scoped lang="scss">
.school-detail {
	.s-nm {
		padding: 10px 0;
		text-align: center;
	}
}
</style>