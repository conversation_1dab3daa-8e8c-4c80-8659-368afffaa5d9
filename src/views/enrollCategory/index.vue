<template>
  <div class="section">
    <div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>{{ entryName }}</span>
		</div>
    <div class="section-main">
      <div class="section-main-content">
        <div
          class="entrance3"
          v-for="item in entranceList"
          :key="item.id"
          @click="entranceNext(item)"
        >
          <div class="entrance-title">{{ item.idsName }}</div>
          <div class="entrance-content">{{ item.detail }}</div>
					<div class="entrance-time">报名开始时间：{{ item.startTime }}</div>
					<div class="entrance-time">报名结束时间：{{ item.endTime }}</div>
        </div>
      </div>
    </div>

    <!-- 招生公告弹窗 -->
    <el-dialog
      title="招生公告"
      :visible.sync="showAnnouncementDialog"
      width="600px"
      center
      :close-on-click-modal="false"
      custom-class="announcement-dialog"
    >
      <div class="announcement-content">
        <p class="announcement-intro">
          鉴于曹妃甸区第二中学和唐山市冀东中学初中部目前均已无空余学位，现主城区可接收区外小学六年级毕业生的学校为曹妃甸区第三完全中学和曹妃甸区实验学校初中部。为了更好地服务家长和学生，教育体育局对这两所学校进行了重新划片，具体如下：
        </p>

        <div class="division-rules">
          <p><strong>长丰路以东：</strong>该区域的学生将被划入曹妃甸区第三完全中学的招生范围。</p>
          <p><strong>长丰路以西：</strong>该区域的学生将被划入曹妃甸区实验学校初中部的招生范围。</p>
        </div>

        <p class="announcement-notice">
          请各位家长根据新的划片办法，并结合家庭实际居住地址，选择相应的学校进行报名。特别提醒，本划片办法仅适用于本次区外小升初招生工作。
        </p>

        <p class="announcement-thanks">感谢您的理解与支持！</p>

        <div class="announcement-footer">
          <p class="department">曹妃甸区教育体育局</p>
          <p class="date">2025年8月4日</p>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="closeAnnouncement">我已知晓</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { 
	getSetupSaveDetail,
	isInAdTimeRange2,
	getNextPath
} from "@/api";
import { Entrance } from "@/mixins/EntranceTips"
export default {
	mixins: [ Entrance ],
  data() {
    return {
      entryName: this.$store.state.entry2.name,
			entryId: this.$store.state.entry2.id,
      entranceList: [],
      showAnnouncementDialog: false, // 控制招生公告弹窗显示
    };
  },
  created() {
		this.$store.commit('setEntry3', '')
		this.$store.commit('setSchoolDetail', {})
    this.getSetupSaveDetail();
    // 显示招生公告弹窗
    this.showAnnouncementDialog = true;
  },
  methods: {
    getSetupSaveDetail() {
      getSetupSaveDetail({ key: this.entryId }).then((res) => {
        const chineseNumbers = {
          '一': 1, '二': 2, '三': 3, '四': 4,
          '五': 5, '六': 6, '七': 7
        };
        
        this.entranceList = res.map(item => {
          const match = item.idsName.match(/第([\u4e00-\u9fa5])批次适龄儿童报名入口/);
          return {
            ...item,
            batchNumber: match && chineseNumbers[match[1]] ? chineseNumbers[match[1]] : 999, 
            startTimeStamp: new Date(item.startTime).getTime() //
          };
        }).sort((a, b) => {
          if (a.batchNumber !== b.batchNumber) {
            return a.batchNumber - b.batchNumber;
          }
          return a.startTimeStamp - b.startTimeStamp;
        });
        
      });
    },
    // 报名入口 - 选择类别
    async entranceNext(item) {
			let isStuAd = await getNextPath({ key: this.$store.state.user.userId })
			// 没报过名, 驳回重报时可以进入
			if (isStuAd == 0) {
				isInAdTimeRange2({
					key: item.setupId
				}).then(res => {
					if (!res) {
						this._entryNotInTime()
					} else {
						this.go2Next(item)
					}
				})
			} else if (isStuAd == 4) {
				// 直接进
				this.go2Next(item)
			} else {
				this.$message.warning('您已报名，修改信息请从录取查询处进入修改')
			}
    },
		// 跳转
		go2Next(item) {
			this.$store.commit('setEntry3', item)
			this.$router.push("/enrollSchool")
		},
    // 关闭招生公告弹窗
    closeAnnouncement() {
      this.showAnnouncementDialog = false;
    }
  }
}
</script>

<style scoped lang="scss">
.section {
	background: #FFF;
}
.section-main-content {
	flex-wrap: wrap;
	align-items: flex-start;
}
.entrance3 {
  flex: 0 0 27%;
	margin-bottom: 40px;
	padding-bottom: 15px;
	border: 1px dotted #a5a5a5;
	border-radius: 4px;
  cursor: pointer;
	.entrance-title {
		padding: 15px 10px;
		text-align: center;
	  background: var(--color);
	  color: #fff;
	  border-radius: 4px 4px 0 0;
	}
	.entrance-content {
	  height: 290px;
		overflow-y: auto;
		margin-bottom: 15px;
	  padding: 15px;
		text-indent: 14px;
	  color: #000;
	}
	.entrance-time {
		text-align: center;
	}
}

/* 招生公告弹窗样式 */
.announcement-dialog .el-dialog__header {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  padding: 20px 24px;
}

.announcement-dialog .el-dialog__title {
  color: white;
  font-weight: bold;
  font-size: 18px;
}

.announcement-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white;
  font-size: 20px;
}

.announcement-content {
  padding: 20px 0;
  line-height: 1.8;
  color: #333;
}

.announcement-intro {
  margin-bottom: 20px;
  text-indent: 2em;
  font-size: 14px;
}

.division-rules {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin: 20px 0;
  border-left: 4px solid #409EFF;
}

.division-rules p {
  margin: 10px 0;
  font-size: 14px;
}

.division-rules strong {
  color: #409EFF;
  font-weight: bold;
}

.announcement-notice {
  margin: 20px 0;
  text-indent: 2em;
  font-size: 14px;
  //color: #E6A23C;
  font-weight: bold;
}

.announcement-thanks {
  margin: 20px 0;
  text-align: center;
  font-size: 14px;
  //color: #67C23A;
  font-weight: bold;
}

.announcement-footer {
  text-align: right;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.department {
  font-weight: bold;
  color: #333;
  margin: 5px 0;
}

.date {
  color: #666;
  margin: 5px 0;
}

.dialog-footer .el-button--primary {
  background: #409EFF;
  border-color: #409EFF;
  padding: 12px 30px;
  font-size: 16px;
}
</style>