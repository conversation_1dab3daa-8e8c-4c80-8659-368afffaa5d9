<template>
  <div class="section">
    <div class="common-title">录取查询</div>
    <div class="section-main">
      <div class="apply-qry">
				<template v-if="pageTp == 0">
					<el-skeleton :rows="6" animated />
				</template>
        <template v-if="pageTp == 1">
          <el-form ref="search" :model="search" :rules="rules" :inline="true">
            <el-form-item prop="idCardNumber" label="学生身份证号">
              <el-input
                v-model.trim="search.idCardNumber"
                placeholder="请输入身份证号"
                style="width: 200px"
                clearable
                maxlength="18"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="searchById"
              ></el-button>
            </el-form-item>
          </el-form>
        </template>
        <template v-if="pageTp == 2">
					<ul class="s-r-list">
					  <li>
					    <span class="desc">录取状态：</span>
					    <span class="res-txt">
								<el-tag size="medium" type="info" v-if="searchResult.enrollStatus == 1">{{ searchResult.enrollStatusName }}</el-tag>
								<el-tag size="medium" type="success" v-else-if="searchResult.enrollStatus == 2">{{ searchResult.enrollStatusName }}</el-tag>
								<el-tag size="medium" type="danger" v-else-if="searchResult.enrollStatus == 3">{{ searchResult.enrollStatusName }}</el-tag>
					    </span>
					  </li>
					  <li>
					    <span class="desc">审核状态：</span>
					    <span class="res-txt" style="color: #909399" v-if="searchResult.auditStatus == 1">{{ searchResult.auditStatusName }}</span>
					    <span class="res-txt" style="color: #67c23a" v-else-if="searchResult.auditStatus == 2">{{ searchResult.auditStatusName }}</span>
					    <span class="res-txt" style="color: #f56c6c" v-else-if="searchResult.auditStatus == 3" >{{ searchResult.auditStatusName }}</span>
					  </li>
						<template v-if="searchResult.auditStatus == 3">
							<template v-if="searchResult.schoolRejectCause && searchResult.educationRejectCause">
								<li>
									<span class="desc">学校驳回原因：</span>
									<span class="res-txt" style="color: #f56c6c">{{ searchResult.schoolRejectCause }}</span>
								</li>
								<li>
									<span class="desc">教育局驳回原因：</span>
									<span class="res-txt" style="color: #f56c6c">{{ searchResult.educationRejectCause }}</span>
								</li>
							</template>
							<template v-else>
								<li>
									<span class="desc">驳回原因：</span>
									<span class="res-txt" style="color: #f56c6c">{{ searchResult.rejectCause }}</span>
								</li>
							</template>
						</template>
					  <li>
					    <span class="desc">学生姓名：</span>
					    <span class="res-txt">{{ searchResult.studentName }}</span>
					  </li>
					  <li>
					    <span class="desc">报名类型：</span>
					    <span class="res-txt">{{ searchResult.registrationType }}</span>
					  </li>
						<li>
							<span class="desc">身份证号：</span>
							<span class="res-txt">{{ searchResult.studentIdCardNumber }}</span>
						</li>
					  <li>
					    <span class="desc">报名学校：</span>
					    <span class="res-txt">{{ searchResult.enrollSchoolName }}</span>
					  </li>
					  <li v-if="searchResult.enrollStatus == 2">
					    <span class="desc">录取学校：</span>
					    <span class="res-txt">{{ searchResult.lastSchoolName }}</span>
					  </li>
					  <li>
					    <span class="desc">报名时间：</span>
					    <span class="res-txt">{{ searchResult.enrollTime }}</span>
					  </li>
					  <li v-if="searchResult.auditTime">
					    <span class="desc">审核时间：</span>
					    <span class="res-txt">{{ searchResult.auditTime }}</span>
					  </li>
					  <li v-if="searchResult.auditStatus == 3">
					    <span class="desc">驳回时间：</span>
					    <span class="res-txt">{{ searchResult.rejectTime }}</span>
					  </li>
					</ul>
					<!-- 录取成功时显示二维码 -->
					<div v-if="shouldShowQRCode" class="qr-code-section">
            <div class="field-tips">
              注:截图后，使用微信扫码即可加入学校群
						</div>
						<div class="qr-code-container">
							<img :src="qrCodeImageUrl" alt="录取二维码" />
						</div>
						<!-- <div class="qr-code-desc">
							请妥善保存此二维码，入学时可能需要使用
						</div> -->
					</div>
					<div class="res-actions">
						<template v-if="searchResult.auditStatus == 2">
							<el-button
							  type="success"
								v-if="searchResult.admissionLetterInStatus == 1"
							  @click="openNotify"
							  >查看录取通知书</el-button
							>
						</template>
						<template v-else>
							<template v-if="searchResult.adjustment == 0">
								<el-button type="info" v-if="jumpTp == 1" @click="go2Detail">查看报名信息</el-button>
								<el-button type="primary" v-else-if="jumpTp == 2" @click="go2DetailAndCheckCount">修改信息</el-button>
								<el-button type="primary" v-else-if="jumpTp == 3" @click="go2Edit">修改信息</el-button>
								<el-button type="warning" v-else-if="jumpTp == 4" @click="startOverAgain">重新报名</el-button>
							</template>
							<template v-else-if="searchResult.adjustment == 1">
								<!-- 调剂状态adjustment是1时只能查看报名详情 -->
								<el-button type="info" v-if="jumpTp == 1" @click="go2Detail">查看报名信息</el-button>
							</template>
						</template>
					</div>
        </template>
      </div>
    </div>
    <el-dialog
      title="查询结果"
      :visible.sync="modal.resByInput"
      width="500px"
      center
    >
      <el-form :model="searchResByInput">
        <el-form-item prop="studentName" label="学生姓名">{{
          searchResByInput.studentName
        }}</el-form-item>
        <el-form-item prop="studentIdCardNumber" label="身份证号">{{
          searchResByInput.studentIdCardNumber
        }}</el-form-item>
      </el-form>
			<el-alert
				title="注：点击【确认】按钮以后信息将无法进行修改，请仔细核对信息，一个公众号只能查询一个学生身份证号，确认信息无误以后，点击【确认】按钮，查询学生信息"
				type="error"
				:closable="false"
			>
			</el-alert>
			<div slot="footer">
				<el-button @click="modal.resByInput = false">取消</el-button>
				<el-button type="primary" @click="confirmBinding">确认</el-button>
			</div>
    </el-dialog>
    <el-dialog
      title="录取通知书"
      :visible.sync="modal.notify"
      width="600px"
      center
    >
      <div class="apply-notify">
        <div v-html="notifyResult.content"></div>
        <p class="school-nm">{{ notifyResult.schoolName }}</p>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="modal.notify = false">确定</el-button>
      </div>
    </el-dialog>
		
  </div>
</template>

<script>
import { idCardValidator } from "@/utils/validator.js";
import { qryFirst, searchResById, notifyDetail, getNextPath, bindIdCard2CurWx } from "@/api";
import { imgPrefix } from '@/utils/common';
export default {
  data() {
    return {
      pageTp: 0,
      // qryFirst参数
      defaultSearch: {
        key: this.$store.state.user.userId,
      },
			// 跳转类型
			jumpTp: -1,
      // searchResById参数
      search: {
        userId: this.$store.state.user.userId,
        idCardNumber: '',
      },
      // 通知书参数
      notifySearch: {
        key: "",
      },
      // searchResById错误信息
      errorMsg: [
        "",
        "请输入有效18位身份证号",
        "未匹配到学生信息，请前往报名入口进行报名，如有疑问请咨询学校或者教育局",
        "未匹配到当前用户微信信息",
        "未匹配到当前学生微信信息",
        `该学生已通过其他微信报名，微信昵称：`
      ],
      // qryFirst结果
      searchResult: {},
      // searchResById结果
      searchResByInput: {
        studentName: "",
        studentIdCardNumber: "",
				studentId: ''
      },
      // notifyDetail结果
      notifyResult: {
        content: "",
        schoolName: "",
      },
      rules: {
        idCardNumber: [
          {
            required: true,
            validator: idCardValidator,
            trigger: "blur",
          },
        ],
      },
      modal: {
        notify: false,
        resByInput: false,
      }
    };
  },
  computed: {
    // 是否应该显示二维码：录取状态为2（录取成功）且有二维码图片
    shouldShowQRCode() {
      return this.searchResult &&
             this.searchResult.enrollStatus == 2 &&
             this.searchResult.qrcodeImage &&
             this.searchResult.qrcodeImage.trim() !== ''
    },
    // 二维码图片完整URL
    qrCodeImageUrl() {
      if (this.searchResult && this.searchResult.qrcodeImage) {
        // 如果已经是完整URL，直接返回
        if (this.searchResult.qrcodeImage.startsWith('http')) {
          return this.searchResult.qrcodeImage
        }
        // 使用项目统一的图片前缀构建URL
        return `${imgPrefix()}${this.searchResult.qrcodeImage}`
      }
      return ''
    }
  },
  created() {
		this.$store.commit('setApplyQryResult', {})
		this.$store.commit('setShowModifyTp', '')
    this.getDataFirst()
  },
  methods: {
		// 决定按钮和跳转类型
		decideJumpTp() {
			getNextPath({ key: this.$store.state.user.userId }).then(res => {
				this.jumpTp = res
			})
		},
    // 查询页面显示类型
    getDataFirst() {
      qryFirst(this.defaultSearch).then((res) => {
        this.pageTp = res.pageType
        for(let i in res) {
        	this.searchResult[i] = res[i]
        }
				this.notifySearch.key = this.searchResult.studentId
				// 驳回时检查驳回类型
				if (this.pageTp == 2 && res.auditStatus != 2) {
					this.decideJumpTp()
				}
      })
    },
    // 根据身份证查询结果
    searchById() {
      this.$refs["search"].validate((valid) => {
        if (valid) {
          searchResById(this.search).then((res) => {
            // 错误提示
            if (
              res.messageType == 1 ||
              res.messageType == 2 ||
              res.messageType == 3 ||
              res.messageType == 4
            ) {
              this.$alert(this.errorMsg[res.messageType], "提示");
            } else if (res.messageType == 5) {
              // 拼接微信昵称
              this.$alert(
                `${this.errorMsg[res.messageType]}${
                  res.result.nickname
                }`,
                "提示"
              );
            } else if (res.messageType == 0) {
              // 查询成功
              this.searchResByInput.studentName = res.result.studentName;
              this.searchResByInput.studentIdCardNumber =
                res.result.studentIdCardNumber;
							this.searchResByInput.studentId = res.result.studentId
              this.modal.resByInput = true;
            }
          });
        } else {
          return false;
        }
      });
    },
    // 返回搜索页
    goBack() {
      this.pageTp = 1;
      this.search.idCardNumber = "";
    },
    // 开启通知书
    openNotify() {
      notifyDetail(this.notifySearch).then((res) => {
        this.notifyResult.content = res.content;
        this.notifyResult.schoolName = res.schoolName;
        this.modal.notify = true;
      });
    },
		// 绑定微信
		confirmBinding() {
			bindIdCard2CurWx({
				userId: this.$store.state.user.userId,
				studentId: this.searchResByInput.studentId
			}).then(res => {
				this.$message.success('绑定成功')
				this.modal.resByInput = false
				this.getDataFirst()
			})
		},
    // 返回
    back() {
      this.$router.push("/home");
    },
		// 查看报名详情
		go2Detail() {
			this.$store.commit('setApplyQryResult', this.searchResult)
			this.$store.commit('setShowModifyTp', 1)
			this.$router.push("/adFormDetail")
		},
		// 查看报名详情并修改信息
		go2DetailAndCheckCount() {
			this.$store.commit('setApplyQryResult', this.searchResult)
			this.$store.commit('setShowModifyTp', 2)
			this.$router.push("/adFormDetail")
		},
		// 直接去修改信息
		go2Edit() {
			this.$store.commit('setApplyQryResult', this.searchResult)
			this.$router.push('/adFormEdit')
		},
		// 驳回重新报名
		startOverAgain() {
			this.$router.replace("/home")
		}
  },
};
</script>

<style scoped lang="scss">
.section {
  width: 1240px;
  min-height: 579px;
  margin: 0 auto 20px;
	border-top: 6px solid #9FD0FF;
  background-color: #FFF;
}
.common-title {
	width: 96%;
	margin: 0 auto;
	border-bottom: 1px dashed #A2C3E8;
	font-size: 16px;
	color: #000;
}
.section-main {
  padding: 0 40px;
}
.common-btn {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}
.apply-qry {
  width: 390px;
  margin: 30px auto 0;
}
.s-r-list {
	width: 100%;
	border: 1px solid #D7E3F7;
	border-bottom: 0 none;
}
.s-r-list > li {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	position: relative;
	width: 100%;
	color: #000;
	border-bottom: 1px solid #D7E3F7;
	&:before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 135px;
		height: 100%;
		background-color: #F1F5FF;
		z-index: 0;
	}
}
.s-r-list .desc {
	position: relative;
	flex: 0 0 135px;
	height: 100%;
	font-weight: bold;
	padding: 15px 0 15px 33px;
	z-index: 0;
}
.s-r-list .res-txt {
	flex: 0 0 calc(100% - 135px);
	padding: 15px;
	word-break: break-all;
}
.res-actions {
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 65px auto;
	padding-bottom: 140px;
}
.apply-notify .school-nm {
  text-align: right;
}

// 二维码样式
.qr-code-section {
  margin: 30px 0;
  text-align: center;
  padding: 20px;
  //background: #f9f9f9;
  border-radius: 8px;
}

.qr-code-title {
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: bold;
  color: #67c23a;
}

.qr-code-container {
  display: inline-block;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.qr-code-container img {
  max-width: 200px;
  max-height: 200px;
  display: block;
}
.field-tips {
  color: red;
  //padding-left: 65px;
  margin-top: -5px;
  margin-bottom: 10px;
}
.qr-code-desc {
  margin-top: 15px;
  color: #909399;
  font-size: 14px;
}
</style>