<template>
  <div class="main-content">
    <div class="main-content-notice">
      <p class="notice-title">
        <span>政策公告</span>
        <span class="notice-more" @click="policyAnnouncement">更多
					<span class="n-m-icon">»</span>
				</span>
      </p>
      <ul class="notice-item">
        <li v-for="item in tableData" :key="item.id">
          <div
            class="notice-item-title"
            @click="policyAnnouncementDtl(item.id)"
          >
						<img src="../assets/img/notice-item.png" class="notice-icon" />
            {{ item.title }}
          </div>
          <div class="notice-item-info">
            <span>{{ item.createTime }}</span>
          </div>
        </li>
      </ul>
    </div>
    <div class="main-content-menu">
      <ul class="menu-entrance">
        <li v-for="item in entranceList" :key="item.id" class="btn" @click="entranceNext(item)">
          <img src="../assets/img/enroll-entry.png" class="btn-icon" />
					{{ item.name }}
        </li>
        <li class="btn" @click="consultingService">
					<img src="../assets/img/hot-line.png" class="btn-icon" />
					<span>咨询电话</span>
        </li>
        <li v-if="showAdmissionButton" class="btn" @click="admissionInquiry">
					<img src="../assets/img/apply-qry.png" class="btn-icon" />
					<span>录取查询</span>
        </li>
      </ul>
    </div>

    <!-- 录取查询通知弹窗 -->
    <el-dialog
      :visible.sync="showNoticeDialog"
      width="400px"
      center
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="notice-dialog"
    >
      <div class="notice-content">
        <div class="notice-title">
          {{ noticeInfo.title }}
        </div>
        <div class="notice-footer">
          <el-button
            type="primary"
            @click="closeNoticeDialog"
            class="confirm-btn"
          >
            我已知晓
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import store from "@/store";
import {
  getPolicyList,
  getSetupSaveIds,
	isInAdTimeRange1,
	getNextPath,
  permissionStatus
} from "@/api";
import { Entrance } from "@/mixins/EntranceTips"
export default {
	mixins: [ Entrance ],
  data() {
    return {
      tableData: [],
      entranceList: [],
      permissionInfo: null, // 权限状态信息
      showAdmissionButton: false, // 是否显示录取查询按钮
      showNoticeDialog: false, // 是否显示通知弹窗
      noticeInfo: {
        title: '8月15日查询录取结果，在这期间本不可查询结果'
      }
    };
  },
  created() {
		this.$store.commit('setEntry1', {})
		this.$store.commit('setEntry2', {})
		this.$store.commit('setEntry3', {})
		this.$store.commit('setSchoolDetail', {})
    this.getSetupSaveIds();
    this.getPolicyAnnouncementList();
    this.getPermissionStatus(); // 获取权限状态
    this.$store.commit("setCurrentNav", "/home");
  },
  methods: {
    // 查询报名入口 - 第一级
    getSetupSaveIds() {
      getSetupSaveIds({ key: 1 }).then((res) => {
        this.entranceList = res
      });
    },
    // 首页查询政策公告列表
    getPolicyAnnouncementList() {
      getPolicyList({
        pageNumber: 1,
        pageSize: 5,
        type: 1,
      }).then((res) => {
        this.tableData = res.records;
      });
    },
    // 获取权限状态
    getPermissionStatus() {
      permissionStatus().then((res) => {
        this.permissionInfo = res;
        // 根据isAnNiu字段控制录取查询按钮显示：1显示，0不显示
        this.showAdmissionButton = res && res.isAnNiu === 1;
        // 检查是否需要显示通知弹窗
        this.$nextTick(() => {
          this.checkShowNoticeDialog();
        });
      }).catch((error) => {
        console.error('获取权限状态失败:', error);
        // 出错时默认不显示按钮
        this.showAdmissionButton = false;
        // 检查是否需要显示通知弹窗
        this.$nextTick(() => {
          this.checkShowNoticeDialog();
        });
      });
    },
    // 关闭通知弹窗
    closeNoticeDialog() {
      this.showNoticeDialog = false;
      // 记录用户已查看通知，避免重复弹出
      localStorage.setItem('noticeDialogShown', 'true');
    },
    // 检查是否需要显示通知弹窗
    checkShowNoticeDialog() {
      // 检查用户是否已经查看过通知
      const hasShown = localStorage.getItem('noticeDialogShown') === 'true';
      // 如果录取查询按钮不显示且用户未查看过通知，则显示弹窗
      if (!this.showAdmissionButton && !hasShown) {
        this.showNoticeDialog = true;
      }
    },
    // 查看政策公告详情
    policyAnnouncementDtl(id) {
      this.$router.push({
        path: "/policyAnnouncementDtl",
        query: { id },
      });
    },
    // 更多按钮
    policyAnnouncement() {
      store.commit("setCurrentNav", "/policyAnnouncement");
      this.$router.push("/policyAnnouncement");
    },
    // 咨询电话
    consultingService() {
      this.$router.push("/consultingService");
      store.commit("setCurrentNav", "/consultingService");
    },
    // 录取查询
    async admissionInquiry() {
      await this.$store.commit("setCurrentNav", "/admissionInquiry");
      await this.$router.push("/admissionInquiry");
    },
    // 报名入口 - 选择乡镇或城区
    async entranceNext(item) {
			let isStuAd = await getNextPath({ key: this.$store.state.user.userId })
			// 没报过名, 驳回重报时可以进入
			if (isStuAd == 0) {
				isInAdTimeRange1().then(res => {
					if (item.id == 2) {
						if (res.villages) {
							this.go2Next(item)
						} else {
							this._entryNotInTime()
						}
					} else if (item.id == 3) {
						if (res.urban) {
							this.go2Next(item)
						} else {
							this._entryNotInTime()
						}
					}
				})
			} else if (isStuAd == 4) {
				// 直接进
				this.go2Next(item)
			} else {
				this.$message.warning('您已报名，修改信息请从录取查询处进入修改')
			}
    },
		// 跳转
		go2Next(item) {
			this.$store.commit('setEntry1', item)
			this.$router.push("/enrollPeriods")
		}
  }
}
</script>

<style scoped>
.main-content {
  display: flex;
  justify-content: space-between;
  width: 1240px;
  /* min-height: 710px; */
  margin: 0 auto;
}

.main-content-notice {
  width: 65%;
  height: 100%;
	box-shadow: 0 0 6px 3px rgba(0, 0, 0, 0.1);
}

.main-content-notice .notice-title {
  display: flex;
  justify-content: space-between;
	background-image: url('../assets/img/home-page-policy-title.png');
  line-height: 115px;
  font-size: 24px;
  padding-left: 56px;
	padding-right: 48px;
  color: #006ABC;
}

.main-content-notice .notice-more {
	color: #FFF;
  font-size: 16px;
  cursor: pointer;
}

.notice-more .n-m-icon {
	display: inline-block;
	vertical-align: -2px;
	font-size: 26px;
	color: #FF9E1E;
}

.main-content-notice .notice-item {
  padding-left: 10px;
  padding-right: 10px;
  height: 380px;
  width: 100%;
	background-color: #F5F6FD;
  overflow-y: auto;
}

.main-content-notice .notice-item li {
	display: flex;
	justify-content: space-between;
	align-items: center;
  line-height: 75px;
  border-bottom: 1px dashed #BAC0DE;
  padding: 0 28px;
}
.main-content-notice .notice-item li:last-child {
	border-bottom: 0 none;
}

.main-content-notice .notice-item li p {
  display: flex;
  justify-content: space-between;
  line-height: 24px;
}

.main-content-notice .notice-item li .notice-item-title {
  color: #006ABC;
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  width: 550px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.main-content-notice .notice-item .notice-icon {
	display: inline-block;
	vertical-align: -7px;
	margin-right: 12px;
}

.main-content-notice .notice-item li .notice-item-info {
  color: #909399;
  font-size: 14px;
}

.main-content-menu {
  width: 33%;
  height: 100%;
}

.main-content-menu .menu-entrance li {
  display: flex;
  justify-content: flex-start;
	align-items: center;
	margin-bottom: 21px;
  padding: 37px 0 37px 143px;
	box-shadow: 0 0 6px 3px rgba(0, 0, 0, 0.1);
	color: #000;
  font-size: 16px;
	background-image: url('../assets/img/home-page-btn-bg.png');
	border-radius: 3px;
	cursor: pointer;
}

/* 通知弹窗样式 */
.notice-dialog {
  border-radius: 8px;
}

.notice-dialog .el-dialog__header {
  display: none;
}

.notice-dialog .el-dialog__body {
  padding: 0;
}

.notice-content {
  background: linear-gradient(135deg, #4A90E2 0%, #7BB3F0 100%);
  border-radius: 8px;
  color: white;
  position: relative;
  overflow: hidden;
  padding: 30px 20px;
}

.notice-title {
  font-size: 16px;
  font-weight: bold;
  color: #FF6B6B;
  text-align: center;
  line-height: 1.4;
  margin-bottom: 30px;
}

.notice-footer {
  text-align: center;
}

.confirm-btn {
  background: #FF6B6B;
  border-color: #FF6B6B;
  color: white;
  padding: 10px 30px;
  font-size: 14px;
  border-radius: 20px;
}

.confirm-btn:hover {
  background: #FF5252;
  border-color: #FF5252;
}
</style>
