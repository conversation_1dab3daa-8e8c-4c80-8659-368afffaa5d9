<template>
	<div class="area-select">
		<p>当前区县：{{ curVal }}</p>
		<ul class="area-list">
			<li v-for="item, idx in list" :key="item.id" class="a-l-item">
				<el-button v-if="cur == item.id" plain disabled type="info" @click="selectArea(item)">{{ item.val }}</el-button>
				<el-button v-else plain type="primary" @click="selectArea(item)">{{ item.val }}</el-button>
			</li>
		</ul>
	</div>
</template>

<script>
import { areaList } from "@/utils/dictionary.js"
export default {
	data() {
		return {
			cur: this.$store.state.deptCode,
			curVal: '',
			list: areaList
		}
	},
	created() {
		this.curVal = areaList.find(v => v.id == this.cur).val
	},
	methods: {
		// 选择区县
		selectArea(item) {
			this.$store.commit('setDeptCode', item.id)
			this.$router.replace('/home')
		}
	}
}
</script>

<style scoped lang="scss">
.area-select {
	padding-top: 20px;
	.area-list {
		margin-top: 20px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}
	.a-l-item {
		margin-bottom: 10px;
		margin-right: 10px;
	}
}	
</style>