import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import persistedState from 'vuex-persistedstate'
import { parentOpenLogin, logout } from '@/api'

Vue.use(Vuex)

//初始化state
const getInitialState = () => {
  return {
    token: '',
		// 额外请求头参数
		state4Headers: '',
    user: {},
    studentInfo: {},
    currentNav: '/home',
    pageNumber: 1,
    deptCode: '130224',
		// 报名1级入口
		entry1: {},
		// 报名2级入口
		entry2: {},
		// 报名3级入口
		entry3: {},
		// 报名学校
		schoolDetail: {},
		// 录取查询result
		applyQryResult: {},
		// 报名详情页是否显示重新报名和修改报名，1不显示修改和重新报名按钮, 2显示
		showModifyTp: 1,
		// 登录方式：wx=微信登录，prov=省平台登录，jsb=冀时办登录
		loginTp: ''
  }
}

export default new Vuex.Store({
  plugins: [
    persistedState({ storage: window.sessionStorage })
  ],
  state: getInitialState(),
  mutations: {
    setToken(state, val) {
      state.token = val
    },
		setState4Headers(state, val) {
			state.state4Headers = val
		},
    setUser(state, val) {
      state.user = val
    },
    setStudentInfo(state, val) {
      state.studentInfo = val
    },
    setCurrentNav(state, val) {
      state.currentNav = val
    },
    setPageNumber(state, val) {
      state.pageNumber = val
    },
    setDeptCode(state, val) {
      state.deptCode = val
    },
    resetState(state) {
      state = Object.assign(state, getInitialState())
    },
		setEntry1(state, val) {
			state.entry1 = val
		},
		setEntry2(state, val) {
			state.entry2 = val
		},
		setEntry3(state, val) {
			state.entry3 = val
		},
		setSchoolDetail(state, val) {
			state.schoolDetail = val
		},
		setApplyQryResult(state, val) {
			state.applyQryResult = val
		},
		setShowModifyTp(state, val) {
			state.showModifyTp = val
		},
		// 登录类型，登出时用到
		setLoginTp(state, val) {
			state.loginTp = val
		}
  },
  actions: {
    //登录
    login({ commit }, obj) {
      return new Promise((resolve, reject) => {
        parentOpenLogin(obj).then(res => {
          commit('setToken', res.token)
          commit('setUser', { userId: res.id, username: res.username, nickname: res.nickname })
					commit('setState4Headers', res.state)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    //退出
    logout({ commit }) {
      return new Promise((resolve, reject) => {
        logout().finally(() => {
          commit('resetState')
          resolve()
        })
      })
    }
  },
  getters
})
