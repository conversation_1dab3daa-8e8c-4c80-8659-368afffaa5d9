@charset "utf-8";
html, body, div, ul, ol, li, p, span, b, strong, em, i, h1, h2, h3, h4, h5, h6 {
    margin: 0;
    box-sizing: border-box;
}

html {
		background-color: #EDF7FF;
    font-size: 14px;
    font-family: "Microsoft YaHei";
    --color: #297ace
}

ul, ol, dl, li, dt, dd {
    padding: 0;
    list-style: none;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
    transition: all .5s;
}

.fade-transform-enter {
    opacity: 0;
    transform: translateX(-30px);
}

.fade-transform-leave-to {
    opacity: 0;
    transform: translateX(30px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    /*高宽分别对应横竖滚动条的尺寸*/
    width: 5px;
    height: 1px;
}

::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    background-color: #409EFF
}

::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ededed;
    border-radius: 10px;
}

.common-btn {
    width: auto;
    font-size: 16px;
    margin-top: 20px;
}

.btns {
    width: 288px;
    margin: 0 auto;
    display: flex;
    justify-content: space-around;
    margin-top: 52px;
}

.container {
    position: relative;
    width: 1240px;
    /*计算高度，内容少时尽可能的不显示滚动条*/
    min-height: calc(100vh - 261px - 130px);
    margin: 0 auto 20px;
    padding-bottom: 60px;
    background-image: linear-gradient(to bottom, rgba(232, 243, 252, 1) 5%, rgba(246, 251, 254, 1) 100%);
}

.section {
	width: 1240px;
	min-height: 579px;
	margin: 0 auto 20px;
	border-radius: 4px;
	.common-title {
		position: relative;
		height: 57px;
		line-height: 55px;
		font-size: 18px;
		font-weight: bold;
		text-align: center;
		color: var(--color);
		.go-back {
			position: absolute;
			top: 50%;
			left: 10px;
			transform: translate(0, -50%);
		}
	}
	.section-main {
		padding: 0 40px;
		.section-main-content {
			margin: 30px;
			min-height: 300px;
			display: flex;
			justify-content: space-around;
			align-items: center;
		}
	}
	.entrance {
	  width: 250px;
	  line-height: 65px;
	  text-align: center;
	  font-size: 16px;
	  background-color: var(--color);
	  border-radius: 4px;
	  color: #fff;
	  cursor: pointer;
	}
	.common-btn {
		position: relative;
		left: 50%;
		transform: translateX(-50%);
	}
}

.page-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 10px 0;
}

.normal-img-upload {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	flex-wrap: wrap;
	.key-desc {
		color: #606266;
		text-align: center;
		min-height: 64px;
	}
	.sample-txt {
		text-align: center;
	}
}

.normal-img-ex {
	.key-desc {
		color: #606266;
		text-align: center;
		min-height: 64px;
	}
	.ph {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 148px;
		height: 148px;
		color: #C0C4CC;
		background-color: #F5F7FA;
	}
}

.ad-form {
	background-color: #FFF;
	padding: 10px;
	.common-title {
		margin-bottom: 10px;
	}
	.ad-form-actions {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.form-group {
		margin: 0;
		.f-group-title {
			margin-bottom: 20px;
			padding: 0 15px;
			line-height: 46px;
			border-radius: 5px;
			background-color: #F1F1F1;
		}
		.both-side {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.f-group-detail {
			margin: 0;
			margin-bottom: 30px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;
			.f-g-d-img-list {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;
				width: 100%;
			}
			.el-form-item {
				flex: 0 0 33%;
				margin-right: 0;
			}
			.normal-item {
				margin-bottom: 22px !important;
			}
			.img-item {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 0 0 14.6666%;
				margin: 10px 1% 0;
			}
			.img-field-label {
				text-align: center;
				color: #606266;
			}
			.img-field-example	{
				text-align: center;
				font-size: 12px;
				line-height: 20px;
				color: #409eff;
			}
		}
		.f-group-tips {
			width: 100%;
			padding-top: 20px;
			padding-left: 30px;
			color: #f56c6c;
			font-size: 13px;
		}
		.sib-actions {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}