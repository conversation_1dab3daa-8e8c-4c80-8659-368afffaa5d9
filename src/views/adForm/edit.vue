<template>
	<div class="section ad-form">
		<div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>修改报名</span>
		</div>
		<el-skeleton
			:loading="loadingAdForm"
			animated
		>
			<template slot="default">
				<el-alert 
					style="margin-bottom: 10px;"
					type="success"
					effect="dark"
					:title="`当前报名学校：${ adSchool }`"
					:closable="false">
				</el-alert>
				<!-- 报名类别 -->
				<dl class="form-group">
					<dt class="f-group-title">报名类别</dt>
					<dd class="f-group-detail">
						<p class="en-tp-desc">报名类别</p>
						<el-select v-model="curEnrollTp" size="small" style="margin-right: 10px;">
							<el-option v-for="item, idx in allEnrollTp" :key="item.setupId" :label="item.idsName" :value="item.setupId"></el-option>
						</el-select>
						<el-button size="small" type="primary" :disabled="curEnrollTp == curPageEnrollTp" @click="confirmEditEnTp">确认修改</el-button>
					</dd>
				</dl>
				<el-form ref='form' :model="form" :rules="rules" size="small" :inline="true" label-width="170px">
					<!-- 普通字段 -->
					<dl v-for="item, idx in normalForm" :key="idx" class="form-group">
						<dt class="f-group-title">{{ item.infoName }}</dt>
						<dd class="f-group-detail">`
							<div>
								<template v-for="fi, fidx in item._normalItem">
									<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
										<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
										<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
									</el-form-item>
                  <div v-if="fi.fieldId == 125" class="field-tipss">
                    注：请填写毕业小学全称
                  </div>
								</template>
							</div>
							<div class="f-g-d-img-list">
								<template v-for="fi, fidx in item._imgItem">
									<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
										<div class="img-item-wrap">
											<span v-if="fi.fieldValue" class="img-del-btn" @click="delImg(fi.fieldId)">×</span>
											<!-- 已经上传了图 -->
											<normal-img-ex v-if="fi.fieldValue" :item-config.sync="fi"></normal-img-ex>
											<!-- 没有上传 -->
											<normal-img-upload v-else :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
										</div>
									</el-form-item>
								</template>
							</div>
							<div class="f-group-tips">
								<div class="tips-item" v-if="item.typeConfigId == 2">
									<p>注：</p>
									<ol>
										<li>含*项均为必填项，请核查后仔细填写，确保无误</li>
										<li>上传的照片大小应小于6M</li>
									</ol>
								</div>
							</div>
						</dd>
					</dl>
					<!-- 双胞胎 -->
					<dl class="form-group" v-if="siblings.allList.length > 0">
						<dt class="f-group-title">
              <el-checkbox v-model="siblings.isHaveSib" @change="initOrHideSib" :disabled="siblings.allList.length === 0">双胞胎信息</el-checkbox>
						</dt>
						<dd class="f-group-detail" v-show="siblings.isHaveSib">
							<ul class="sib-list">
								<li class="sib-item" v-for="si, sIdx in siblings.list" :key="sIdx">
									 <el-divider content-position="left">双胞胎{{ sIdx + 1 }}</el-divider>
									<div>
										<template v-for="fi, fidx in si._normalItem">
											<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
												<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
												<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
											</el-form-item>
										</template>
									</div>
									<div class="f-g-d-img-list">
										<template v-for="fi, fidx in si._imgItem">
											<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
												<div class="img-item-wrap">
													<span v-if="fi.fieldValue" class="img-del-btn" @click="delImg(fi.fieldId)">×</span>
													<!-- 已经上传了图 -->
													<normal-img-ex v-if="fi.fieldValue" :item-config.sync="fi"></normal-img-ex>
													<!-- 没有上传 -->
													<normal-img-upload v-else :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
												</div>
											</el-form-item>
										</template>
									</div>
								</li>
							</ul>
							<div class="sib-actions">
								<el-button size="small" type="primary" @click="sibAdd" v-if="siblings.list.length == 1">添加</el-button>
								<el-button size="small" type="danger" @click="sibDel" v-if="siblings.list.length == 2">删除</el-button>
							</div>
						</dd>
					</dl>
					<!-- 随迁 -->
					<dl class="form-group" v-if="sq.isSQ">
						<dt class="f-group-title both-side">
							<span>{{ sq.tpCn }}</span>
							<el-radio-group v-model="sq.tpCn" @input="sqTpChange" size="mini">
								<el-radio-button :key="sqRadioItem.typeConfigId" v-for="sqRadioItem in sq.allList" :label="sqRadioItem.infoName"></el-radio-button>
							</el-radio-group>
						</dt>
						<dd class="f-group-detail">
							<div v-for="sqItem in sq.list" :key="sqItem.typeConfigId">
								<div>
									<template v-for="fi, fidx in sqItem._normalItem">
										<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
											<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
											<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>

                    </el-form-item>
									</template>
								</div>
								<div class="f-g-d-img-list">
									<template v-for="fi, fidx in sqItem._imgItem">
										<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
											<div class="img-item-wrap">
												<span v-if="fi.fieldValue" class="img-del-btn" @click="delImg(fi.fieldId)">×</span>
												<!-- 已经上传了图 -->
												<normal-img-ex v-if="fi.fieldValue" :item-config.sync="fi"></normal-img-ex>
												<!-- 没有上传 -->
												<normal-img-upload v-else :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
											</div>
										</el-form-item>
									</template>
								</div>
							</div>
						</dd>
					</dl>
					<!-- 房产 -->
					<dl class="form-group" v-if="propertyForm.list.length > 0">
						<dt class="f-group-title">房产信息</dt>
						<dd class="f-group-detail">
							<el-form-item label="房产类型">
								<el-select style="width: 220px" size="small" v-model="propertyForm.tp" @change="ppTabChange">
									<el-option v-for="ppItem in propertyForm.list" :value="ppItem.typeConfigId" :label="ppItem.infoName" :key="ppItem.typeConfigId"></el-option>
								</el-select>
							</el-form-item>
							<div v-for="ppItem in propertyForm.curList" :key="ppItem.typeConfigId">
								<div>
									<template v-for="fi, fidx in ppItem._normalItem">
										<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
                      <normal-input :ref="fi.fieldId" :item-config.sync="fi" :isNumber="propertyForm.tp == 9 && fi.fieldId == 71" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                      <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
										</el-form-item>
                    <!-- 为不动产权证书的房屋坐落地址添加提示 -->
                    <div v-if="propertyForm.tp == 9 && fi.fieldId == 148" class="field-tips">
                      注：请输入不动产权证第X号中的数字部分 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：地址从小区名称开始填写
                    </div>

                    <div v-if="propertyForm.tp == 8 && fi.fieldId == 145" class="field-tips">
                      注：请输入房产证编号中'X字第'后面的数字部分 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：地址从小区名称开始填写
                    </div>

									</template>
								</div>
								<div class="f-g-d-img-list">
									<template v-for="fi, fidx in ppItem._imgItem">
										<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
											<div class="img-item-wrap">
												<span v-if="fi.fieldValue" class="img-del-btn" @click="delImg(fi.fieldId)">×</span>
												<!-- 已经上传了图 -->
												<normal-img-ex v-if="fi.fieldValue" :item-config.sync="fi"></normal-img-ex>
												<!-- 没有上传 -->
												<normal-img-upload v-else :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
											</div>
										</el-form-item>
									</template>
								</div>
							</div>
						</dd>
					</dl>
					<template v-if="others.list.length > 0">
						<dl v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId" class="form-group">
							<dt class="f-group-title">其他材料证明</dt>
							<dd class="f-group-detail">
								<div>
									<template v-for="fi, fidx in oItem._normalItem">
										<el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
											<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
											<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
										</el-form-item>
									</template>
								</div>
								<template v-for="fi, fidx in oItem._imgItem">
									<el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
										<div class="img-item-wrap">
											<span v-if="fi.fieldValue" class="img-del-btn" @click="delImg(fi.fieldId)">×</span>
											<!-- 已经上传了图 -->
											<normal-img-ex v-if="fi.fieldValue" :item-config.sync="fi"></normal-img-ex>
											<!-- 没有上传 -->
											<normal-img-upload v-else :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
										</div>
									</el-form-item>
								</template>
							</dd>
						</dl>
					</template>
				</el-form>
				<div style="display: flex; justify-content: center; align-items: center;">
					<el-button @click="_goBack()">返回</el-button>
					<el-button type="primary" :loading="submitDisable" @click="submitAdForm">提交</el-button>
				</div>
			</template>
		</el-skeleton>
	</div>
</template>

<script>
import {getAllAdTp1, qryEditAdForm, submitAdSecond} from "@/api/index.js"
import NormalInput from "@/components/Form/NormalInput"
import NormalSelect from "@/components/Form/NormalSelect"
import NormalImgUpload from "@/components/Form/NormalImgUpload"
import NormalImgEx from "@/components/Exhibition/NormalImgEx"
import {LoopFn} from "@/mixins/loopFn"

export default {
	components: {
		NormalInput,
		NormalSelect,
		NormalImgUpload,
		NormalImgEx
	},
	mixins: [ LoopFn ],
	data() {
		return {
			// 所有报名类型
			allEnrollTp: [],
			// 当前报名类型, 选择框的值
			curEnrollTp: '',
			// 页面展示的报名类型,
			// 切换了新报名类型，填写完信息再去修改curEnrollTp会导致提交类型与页面展示类型不一致
			curPageEnrollTp: '',
			// 录取查询来的报名信息
			applyRes: this.$store.state.applyQryResult,
			// 要报名的学校
			adSchool: ''
		}
	},
	created() {
		this.adSchool = this.applyRes.enrollSchoolName
		this.curEnrollTp = this.applyRes.registrationTypeId
		// 是否随迁类型
		this.sq.isSQ = this.sq.idList.some(v => v == this.curEnrollTp)
		this.getAdForm()
		this.getAllEnrollTp()
	},
	methods: {
		// 获取所有报名类型
    getAllEnrollTp() {
      getAllAdTp1({
        key: this.applyRes.enrollSchoolId
      }).then(res => {
        this.allEnrollTp = res
      })
    },
		// 修改报名类型
		confirmEditEnTp() {
			this.$confirm('部分已填信息将被清空，确定修改报名类型？', '提示', {
				type: 'warning'
			}).then(() => {
				// 重置房产
				this.propertyForm.tp = 0
				this.propertyForm.lastTp = 0
				this.propertyForm.list = []
				this.propertyForm.curList = []
				// 重置双胞胎
				this.siblings.allList = []
				this.siblings.list = []
				this.siblings.isHaveSib = false
				// 重置其它
				this.others.list = []
				// 重置随迁
				this.sq.isSQ = this.sq.idList.some(v => v == this.curEnrollTp)
				this.sq.lastTp = ''
				this.sq.tp = ''
				this.sq.tpCn = ''
				this.sq.allList = []
				this.sq.list = []
				this.loadingAdForm = true
				this.getAdForm()
			}).catch(() => {})
		},
		// 获取报名字段
		getAdForm() {
			qryEditAdForm({
				setupId: this.curEnrollTp,
				studentId: this.applyRes.studentId
			}).then(res => {
				// 按typeConfigId从小到大排序
				let resCopy = JSON.parse(JSON.stringify(res.enrollMiddleFieldVoList)).sort((a, b) => a.typeConfigId - b.typeConfigId)
				this.curPageEnrollTp = this.curEnrollTp
				// 分类
				this.originList = resCopy.map(this.separateImgAndNormal)
				// 是随迁
				if (this.sq.isSQ) {
					// 经商5、务工6、居住证7改为三选一，单独处理
					let normalIdList = [1, 2, 4].map(v => `${ v }`)
					this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
					// 获取经商、务工、居住证信息，并按居住证、经商、务工的顺序排列
					this.sq.allList = this.originList.filter(v => v.typeConfigId == 5 || v.typeConfigId == 6 || v.typeConfigId == 7)
						.sort((a, b) => {
							// 居住证(7)排第一，经商(5)排第二，务工(6)排第三
							const order = { 7: 1, 5: 2, 6: 3 }
							return order[a.typeConfigId] - order[b.typeConfigId]
						})
					// 如果配置了经商、务工、居住证
					if (this.sq.allList.length > 0) {
						// 其它不包含经商务工居住证的报名类型修改为随迁，followWorkType为0
						if (res.followWorkType == 0) {
							// 默认选中第一个可用选项（现在居住证7排在第一位）
							this.sq.tp = this.sq.allList[0].typeConfigId
						} else {
							// 否则直接取已保存的值
							this.sq.tp = `${ res.followWorkType }`
						}
						// 确保选中的类型在可用列表中
						const selectedItem = this.sq.allList.find(v => v.typeConfigId == this.sq.tp)
						if (selectedItem) {
							this.sq.tpCn = selectedItem.infoName
							this.sqTpChange(this.sq.tpCn)
						} else {
							// 如果选中的类型不在列表中，默认选择第一个（现在居住证排在第一位）
							this.sq.tp = this.sq.allList[0].typeConfigId
							this.sq.tpCn = this.sq.allList[0].infoName
							this.sqTpChange(this.sq.tpCn)
						}
					}
				} else {
					// 不是随迁，直接包含5、6、7
					let normalIdList = [1, 2, 4, 5, 6, 7].map(v => `${ v }`)
					this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
				}
				// 房产字段：typeConfigId >= 8但小于18
				this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 &&  v.typeConfigId < 18)
				// 如果有房产
				if (this.propertyForm.list.length > 0) {
					// 如果上个报名类型没有房产字段
					if (res.houseInfoType == 0) {
						// 选中第1个房产类型
						this.propertyForm.tp = this.propertyForm.list[0].typeConfigId
					} else {
						this.propertyForm.tp = `${ res.houseInfoType }`
					}
					this.propertyForm.lastTp = this.propertyForm.tp
					this.propertyForm.curList = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
					// 添加第1个tab的验证规则
					this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp).map(this.addValidRules)
				}
				// 双胞胎字段：typeConfigId为3和19
				this.siblings.allList = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
        if (this.siblings.allList.length > 0) {
          this.siblings.isHaveSib = true
          // this.siblings.isHaveSib = true
          this.siblings.list = JSON.parse(JSON.stringify(this.siblings.allList))
          //需要判断是否是真实数据还是后端自动创建的回填数据
          // 检查是否是有效数据（非空模板）
          const hasRealData = this.siblings.list.some(sibling => {
            return sibling._normalItem.some(field => field.fieldValue) ||
                sibling._imgItem.some(field => field.fieldValue);
          });
          // 只有真实数据才默认勾选
          this.siblings.isHaveSib = hasRealData;

          // 是否打开父级开关
          this.siblings.isHaveSib = this.siblings.allList.some(v => v.leafFieldInfos.some(v1 => v1.fieldValue))
          // 打开了
          if (this.siblings.isHaveSib) {
            // 是否打开第2个多胞胎
            let isOpenSecondSib = this.siblings.allList.filter(v => v.typeConfigId == 19).some(v => v.leafFieldInfos.some(v1 => v1.fieldValue))
            if (!isOpenSecondSib) {
              this.sibDel()
            }
          }
        }
				// 其他补充信息
				this.others.list = this.originList.filter(v => v.typeConfigId == 18)
				if (this.others.list.length > 0) {
					this.others.list.map(this.addValidRules)
				}
				// 加载完成
				this.loadingAdForm = false
			})
		},
		// 删除已上传并显示uploader
		delImg(id) {
			this.form[id] = ''
			this.changeVal(id, '')
		},
		// 提交
		submitAdForm() {
			this.submitDisable = true
			let params = {
				setUpSaveIds: this.curPageEnrollTp,
				enrollSchoolId: this.applyRes.enrollSchoolId,
				enrollSchoolName: this.applyRes.enrollSchoolName,
				userId: this.$store.state.user.userId,
				enrollMiddleFieldFormList: this.originList,
				houseType: this.propertyForm.tp,
				followWorkType: ''
			}
			// 没选双胞胎就去掉上传字段
			if (!this.siblings.isHaveSib) {
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 3 && v.typeConfigId != 19)
			} else if (this.siblings.list.length == 1) {
				// 只有一条时删掉id是19的
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 19)
			}
			// 是随迁传入选中的随迁类型
			if (this.sq.isSQ) {
				params.followWorkType = this.sq.tp
			}
			this.$refs['form'].validate(valid => {
				if (valid) {
					submitAdSecond(params).then(res => {
						this.$message.success('修改报名成功')
						this.$router.replace('/home')
						this.submitDisable = false
					}).catch(err => {
						this.submitDisable = false
					})
				} else {
					this.submitDisable = false
					return false
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.form-group {
	.f-group-detail {
		.img-item-wrap {
			position: relative;
			border-radius: 6px;
		}
		.img-item-wrap .img-del-btn {
			position: absolute;
			top: 0;
			right: 0;
			width: 20px;
			line-height: 20px;
			color: #FFF;
			text-align: center;
			background-color: #F56C6C;
			z-index: 1;
			cursor: pointer;
		}
    .field-tips {
      color: red;
      padding-left: 65px;
      margin-top: -5px;
      margin-bottom: 10px;
    }
    .field-tipss {
      color: red;
      padding-left: 489px;
    }
		.en-tp-desc {
			display: inline-block;
			width: 170px;
			line-height: 32px;
			text-align: right;
			vertical-align: middle;
			color: #606266;
			padding-right: 12px;
		}
	}
}
</style>