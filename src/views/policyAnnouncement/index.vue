<template>
  <div class="policyAnnouncement section">
		<div class="common-title">
			<span>政策公告</span>
		</div>
    <div class="policyAnnouncement-main">
      <ul class="notice-item">
        <li v-for="item in tableData" :key="item.id">
          <div
            class="notice-item-title"
            @click="policyAnnouncementDtl(item.id)"
          >
            {{ item.title }}
          </div>
          <div class="notice-item-info">
            <span>发布时间：{{ item.createTime }}</span>
          </div>
        </li>
      </ul>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNumber"
        :page-size="queryParams.pageSize"
        layout="total, prev, pager, next"
        :total="total"
        background
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import { mapState } from "vuex";
import { getPolicyList } from "@/api";
export default {
  data() {
    return {
      queryParams: {
        pageNumber: 1,
        pageSize: 10,
        type: 1,
      },
      total: 0,
      tableData: [],
    };
  },
  created() {
    // console.log(this.pageNumber)
    this.queryParams.pageNumber = this.pageNumber;
    this.getList();
  },
  computed: {
    ...mapState(["pageNumber"]),
  },
  // 离开路由时,禁用keepAlive
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false;
    next();
  },
  methods: {
    //查询列表
    getList() {
      getPolicyList(this.queryParams).then((res) => {
        this.tableData = res.records;
        this.total = Number(res.total);
      });
    },

    //查看政策公告详情
    policyAnnouncementDtl(id) {
      this.$router.push({
        path: "/policyAnnouncementDtl",
        query: { id },
      });
    },

    //每页显示条数
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },

    //当前页码
    handleCurrentChange(val) {
      this.queryParams.pageNumber = val;
      this.getList();
      // 存储当前页码
      store.commit("setPageNumber", val);
    },
  },
};
</script>

<style scoped>
.policyAnnouncement {
  width: 1240px;
  height: 579px;
  margin: 0 auto 20px;
  background-image: linear-gradient(
    to bottom,
    rgba(232, 243, 252, 1) 5%,
    rgba(246, 251, 254, 1) 100%
  );
}
.policyAnnouncement-main {
  width: 990px;
  margin: 0 auto;
}
.policyAnnouncement-main .notice-item {
  margin-top: 10px;
  height: 445px;
  overflow-y: auto;
  padding-left: 10px;
  padding-right: 10px;
}
.policyAnnouncement-main .notice-item li {
  height: 64px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.policyAnnouncement-main .notice-item li p {
  display: flex;
  justify-content: space-between;
  line-height: 24px;
}
.policyAnnouncement-main .notice-item li .notice-item-title {
  color: #606266;
  font-weight: bold;
  font-size: 15px;
  cursor: pointer;
  width: 600px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.policyAnnouncement-main .notice-item li .notice-item-info {
  color: #909399;
  font-size: 14px;
}
.el-pagination {
  text-align: center;
  margin-top: 20px;
}
</style>