<template>
    <div>
			<div class="gradient-bg">
			</div>
			<Header />
			<Main />
			<Footer />
    </div>
</template>

<script>
import Header from "@/views/layout/components/Header";
import Main from "@/views/layout/components/Main";
import Footer from "@/views/layout/components/Footer";

export default {
    components: { Header, Main, Footer },
};
</script>

<style>
.gradient-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 378px;
	background-image: linear-gradient(180deg, #51A6FD, transparent);
	z-index: -1;
}
.main {
    margin-top: 261px;
}
</style>