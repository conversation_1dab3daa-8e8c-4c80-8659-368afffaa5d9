<template>
  <div class="normal-img-upload">
    <div>
      <el-upload
        list-type="picture-card"
        :class="{ 'hide-upload': hideUpload }"
        :action="action"
        :headers="headers"
        :file-list="fileList"
        :multiple="multi"
        :limit="maxCount"
        :before-upload="beforeUploadPic"
        :on-remove="removePic"
        :on-success="uploadSucc"
        :on-preview="previewPic"
        :on-exceed="exceedMax"
      >
        <i class="el-icon-plus"></i>
      </el-upload>
    </div>
    <div class="key-desc" :class="{ 'is-required': isRequired }">
      {{ keyDesc }}
    </div>
    <div class="sample-txt" v-if="sampleUrl">
      <el-button type="text" size="mini" @click="previewSamplePic"
        >示例图</el-button
      >
    </div>
    <!-- 预览 -->
    <el-dialog
      :visible.sync="showImgDialog"
      :title="keyDesc"
      width="800px"
      center
    >
      <img width="100%" :src="previewSrc" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import { delImgUpload } from "@/api/index.js";
import store from "@/store";
import * as imageConversion from "image-conversion";
import { pref } from "@/utils/common"
import { imgPrefix } from '@/utils/common'
export default {
  name: "normal-img-upload",
  data() {
    return {
      action: "",
      accept: ".jpg, .png, .jpeg",
      isRequired: false,
      headers: {
        Authorization: `${store.state.token}`,
				'client-id': 'pc',
				state: store.state.state4Headers
      },
      keyDesc: "",
      sampleUrl: "",
      fileList: [],
      previewSrc: "",
      showImgDialog: false,
      maxCount: 1,
      hideUpload: false,
      multi: false,
      // 大小
      size: 6,
    };
  },
  props: {
    // 字段所有配置
    itemConfig: {
      type: Object,
      required: true,
    },
    // 多选
    multiple: {
      type: Boolean,
      default: false,
    },
    // 最大上传个数
    limit: {
      type: Number,
      default: 1,
    },
  },
  created() {
    this.keyDesc = this.itemConfig.fieldName;
    this.sampleUrl = this.itemConfig.sampleImgUrl;
    this.maxCount = this.limit;
    this.multi = this.multiple;
    this.isRequired = this.itemConfig.isNecessary == 1;
    this.action =
      process.env.VUE_APP_BASE_API +
      pref +
      this.$store.state.deptCode +
      "/biz/file/uploadImg";
  },
  methods: {
    // 删除
    removePic(file, fileList) {
      delImgUpload({
        key: file.response.data,
      }).then((res) => {
        this.valChange("");
        this.hideUpload = fileList.length >= this.maxCount;
        this.$message.success(`图片已删除`);
      });
    },
    // 检查大小
    beforeUploadPic(file) {
      if (file.size >= this.size * 1024 * 1024) {
        this.$message.warning(`图片大小不能大于${this.size}MB`);
        return false;
      } else {
        return new Promise((resolve, reject) => {
          // 图片大于200k时
          if (file.size > 200 * 1024) {
          		// 就压缩到200KB
          		imageConversion.compressAccurately(file, 200).then(res => {
          			resolve(res)
          		})
          } else {
          	resolve(file)
          }
        })
      }
    },
    // 超过个数
    exceedMax() {
      this.$message.warning(`最多上传${this.maxCount}个文件`);
    },
    // 预览上传
    previewPic(file) {
      this.previewSrc = file.url;
      this.showImgDialog = true;
    },
		// 预览示例图
		previewSamplePic() {
		  this.previewSrc = `${ imgPrefix() }${ this.sampleUrl }`
		  this.showImgDialog = true;
		},
    // 上传成功
    uploadSucc(response, file, fileList) {
      this.valChange(response.data);
      this.hideUpload = fileList.length >= this.maxCount;
    },
    // emit
    valChange(data) {
      // this.modelData = data
      this.$emit("value-change", {
        id: this.itemConfig.fieldId,
        val: data,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.normal-img-upload {
  .hide-upload {
    ::v-deep .el-upload--picture-card {
      display: none !important;
    }
    ::v-deep .el-upload-list__item {
      margin: 0;
    }
  }
  .is-required:before {
    content: "*";
    color: #f56c6c;
    margin-right: 4px;
  }
}
</style>