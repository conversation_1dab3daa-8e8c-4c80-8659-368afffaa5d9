<template>
	<div class="normal-select">
		<!-- 日期选择 -->
		<el-date-picker 
			v-if="verCode == 2 || verCode == 14"
			v-model="modelData"
			type="date"
			value-format="yyyy-MM-dd"
			:placeholder="phTxt"
		></el-date-picker>
		<!-- 其它下拉选择 -->
		<el-select
			v-else
			clearable
			style="width: 220px;"
			v-model="modelData" 
			:placeholder="phTxt"
			:filterable="isFilterable"
		>
			<el-option v-for="item, idx in dataSource" :key="idx" :label="item.val" :value="item.id"></el-option>
		</el-select>
	</div>
</template>

<script>
import { rulesList } from "@/utils/dictionary"
import { deptPageList, schoolAdRange,xiaoquList} from "@/api/index"
export default {
	name: 'normal-select',
	data() {
		return {
			modelData: '',
			phTxt: '',
			// 录取查询来的报名信息
			applyRes: this.$store.state.applyQryResult,
			dataSource: [],
			isFilterable: false
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		}
	},
	watch: {
		modelData(newV, oldV) {
			if (newV != oldV) {
				this.valChange(newV)
			}
		}
	},
	computed: {
		// 验证规则的快捷方式
		verCode() {
			return this.itemConfig.infoVerificationCode
		}
	},
	created() {
		this.phTxt = `请选择${ this.itemConfig.fieldName }`
		// 报名修改页面：如果有值，直接填充
		if (this.itemConfig.fieldValue) {
			this.modelData = this.itemConfig.fieldValue
		}
		// inputItemCode为6时开启filterable属性
		this.isFilterable = this.itemConfig.inputItemCode == 6
		// infoVerificationCode为3，4，5，9，10，12，13时直接读取字典里的list
		let dicListIdx = [3, 4, 5, 9, 10, 12, 13]
		// infoVerificationCode为6，7，8时需要请求api获取数据
		let qryListIdx = [6, 7, 8,23]
		if (dicListIdx.indexOf(this.verCode) != -1) {
			this.dataSource = rulesList[this.verCode].list
		} else if (qryListIdx.indexOf(this.verCode) != -1) {
			if (this.verCode == 6) {
				this.primarySchoolList()
			} else if (this.verCode == 7) {
				this.preSchoolList()
			} else if (this.verCode == 8) {
				this.rangeList()
			}else if(this.verCode == 23) {
        this.xiaoquList()
      }
		}
	},
	methods: {
		// emit
		valChange(data) {
			this.modelData = data
			this.$emit('value-change', {
				id: this.itemConfig.fieldId,
				val: data
			})
		},
		// infoVerificationCode == 6时，小学列表
		primarySchoolList() {
			let params = {
				keywords: "",
				nature: '',
				// 学段
				period: 2,
				deptCode: this.$store.state.deptCode,
				type: 1,
				pageNumber: 1,
				pageSize: 9999
			}
			deptPageList(params).then(res => {
				res.records.forEach(v => {
					v.val = v.deptName
				})
				this.dataSource = res.records
			})
		},
		// infoVerificationCode == 7时，幼儿园列表
		preSchoolList() {
			let params = {
				keywords: "",
				nature: '',
				// 学段
				period: 1,
				deptCode: this.$store.state.deptCode,
				type: 1,
				pageNumber: 1,
				pageSize: 9999
			}
			deptPageList(params).then(res => {
				res.records.forEach(v => {
					v.val = v.deptName
				})
				this.dataSource = res.records
			})
		},
    // infoVerificationCode == 23时，小区列表
    xiaoquList() {
      let params = {
        key:this.$store.state.schoolDetail.id,
      }
      xiaoquList(params).then(res => {
        // API返回的数据直接是数组格式
        let dataList = res || []
        dataList.forEach(v => {
          v.val = v.rangeName
        })
        this.dataSource = dataList
      })
    },
		// infoVerificationCode == 8时，范围列表
		rangeList() {
			let params = {
				type: 2
			}
			// 如果已有报名学校，取报名学校id
			if (this.applyRes.enrollSchoolId) {
				params.schoolId = this.applyRes.enrollSchoolId
			} else {
				// 否则取已选择的学校id
				params.schoolId = this.$store.state.schoolDetail.id
			}
			schoolAdRange(params).then(res => {
				res.forEach(v => {
					v.val = v.rangeName
				})
				this.dataSource = res
			})
		},
	}
}
</script>
