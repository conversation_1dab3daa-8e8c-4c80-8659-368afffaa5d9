<template>
  <div class="section">
    <div class="common-title">
			<el-button class="go-back" type="primary" icon="el-icon-back" @click="_goBack()" circle></el-button>
			<span>小学招生范围</span>
		</div>
    <div class="section-main">
      <div class="content" v-html="content"></div>
    </div>
  </div>
</template>

<script>
import { getPolicyDetail } from "@/api";
export default {
  data() {
    return {
			content: ''
    };
  },
  created() {
    this.getData();
  },
  methods: {
    // 获取小学招生范围
    getData() {
      getPolicyDetail({ key: 1 }).then((res) => {
        this.content = res.content
      });
    },
    // 返回
    back() {
      this.$router.push("/enrollRange");
    },
  },
};
</script>

<style scoped>
.section {
  background-image: linear-gradient(
    to bottom,
    rgba(232, 243, 252, 1) 5%,
    rgba(246, 251, 254, 1) 100%
  );
}
.section-main {
  padding: 0 40px;
}
.section-main .content {
  padding-bottom: 100px;
}
.section-main .content >>> img {
  max-width: 100%;
  height: auto;
}
</style>